/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator, IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { <PERSON><PERSON>ode<PERSON><PERSON>or, IViewZone } from '../../../../editor/browser/editorBrowser.js';
import { ICodeEditorService } from '../../../../editor/browser/services/codeEditorService.js';
import { ComputedDiff, findDiffs } from './helpers/findDiffs.js';
import { EndOfLinePreference, IModelDecorationOptions, ITextModel } from '../../../../editor/common/model.js';
import { IRange } from '../../../../editor/common/core/range.js';
import { IModelService } from '../../../../editor/common/services/model.js';
import { IUndoRedoElement, IUndoRedoService, UndoRedoElementType } from '../../../../platform/undoRedo/common/undoRedo.js';
import { RenderOptions } from '../../../../editor/browser/widget/diffEditor/components/diffEditorViewZones/renderLines.js';
import { ScrollType } from '../../../../editor/common/editorCommon.js';

import { URI } from '../../../../base/common/uri.js';
import { IConsistentEditorItemService, IConsistentItemService } from './helperServices/consistentItemService.js';
import { codeseekPrefixAndSuffix, ctrlKStream_userMessage, ctrlKStream_systemMessage, defaultQuickEditFimTags, rewriteCode_systemMessage, rewriteCode_userMessage, searchReplace_systemMessage, searchReplace_userMessage, rewriteCodeUserMessage, rewriteCodeSystemMessage, } from './../common/prompt/prompts.js';

import { mountCtrlK } from './react/out/quick-edit-tsx/index.js';
import { QuickEditPropsType } from './quickEditActions.js';
import { IModelContentChangedEvent } from '../../../../editor/common/textModelEvents.js';
import { extractCodeFromFIM, extractCodeFromRegular, ExtractedSearchReplaceBlock, extractSearchReplaceBlocks } from './helpers/extractCodeFromResult.js';
import { filenameToVscodeLanguage } from '../common/helpers/detectLanguage.js';
import { INotificationService, Severity } from '../../../../platform/notification/common/notification.js';
import { isMacintosh } from '../../../../base/common/platform.js';
import { EditorOption } from '../../../../editor/common/config/editorOptions.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { CODESEEK_OPEN_SETTINGS_ACTION_ID } from './codeseekSettingsPane.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { ILLMMessageService } from '../common/llmMessageService.js';
import { LLMChatMessage, OnError, errorDetails } from '../common/llmMessageTypes.js';
import { IMetricsService, METRICS_EVENT, METRICS_EVENT_TYPE } from '../common/metricsService.js';
import { ICodeseekFileService } from '../common/codeseekFileService.js';
import { CODESEEK_OPEN_SIDEBAR_ACTION_ID, CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID } from './sidebarActions.js';
import { CODESEEK_CTRL_K_ACTION_ID, CODESEEK_EXPLAIN_CODE_ACTION_ID, CODESEEK_REFACTOR_CODE_ACTION_ID } from './actionIDs.js';
import { ISidebarStateService } from './sidebarStateService.js';
import { TextSelectionTipWidget } from './media/textSelectionTipWidget.js';
import { BlankLineTipWidget } from './media/blankLineTipWidget.js';
import { AcceptAllRejectAllWidget } from './media/acceptAllRejectAllWidget.js';
import { AcceptRejectWidget } from './media/acceptRejectWidget.js';
import { IQuickEditStateService } from './quickEditStateService.js';
import { FeatureNames, ModelSelection, ProviderNames } from '../common/codeseekSettingsTypes.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { ILifecycleService } from '../../../services/lifecycle/common/lifecycle.js';
import { IEditorService } from '../../../services/editor/common/editorService.js';
import { getWorkspaceUri } from '../common/helpers/path.js';
import { StagingSelectionItem } from '../common/selectedFileServiceType.js';
import { ICodeSeekExporerService } from '../common/codeseekExporerService.js';
import { ICodeseekSettingsService } from '../common/codeseekSettingsService.js';
import { IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';
import { generateUuid } from '../../../../base/common/uuid.js';
import { ICodeseekUacLoginService } from '../common/uac/UacloginTypes.js';
import { IWorkingCopyService } from '../../../services/workingCopy/common/workingCopyService.js';
import { ILargeFileApplyService } from './fastApply/largeFileApplyService.js';
import { EditorActivation } from '../../../../platform/editor/common/editor.js';
import { ITextModelService } from '../../../../editor/common/services/resolverService.js';

const getLeadingWhitespacePx = (editor: ICodeEditor, startLine: number): number => {

	const model = editor.getModel();
	if (!model) {
		return 0;
	}

	// Get the line content, defaulting to empty string if line doesn't exist
	const lineContent = model.getLineContent(startLine) || '';

	// Find the first non-whitespace character
	const firstNonWhitespaceIndex = lineContent.search(/\S/);

	// Extract leading whitespace, handling case where line is all whitespace
	const leadingWhitespace = firstNonWhitespaceIndex === -1
		? lineContent
		: lineContent.slice(0, firstNonWhitespaceIndex);

	// Get font information from editor render options
	const { tabSize: numSpacesInTab } = model.getFormattingOptions();
	const spaceWidth = editor.getOption(EditorOption.fontInfo).spaceWidth;
	const tabWidth = numSpacesInTab * spaceWidth;

	let paddingLeft = 0;
	for (const char of leadingWhitespace) {
		if (char === '\t') {
			paddingLeft += tabWidth;
		} else if (char === ' ') {
			paddingLeft += spaceWidth;
		}
	}

	return paddingLeft;
};



// finds block.orig in fileContents and return its range in file
// startingAtLine is 1-indexed and inclusive
const findTextInCode = (text: string, fileContents: string, startingAtLine?: number) => {
	const idx = fileContents.indexOf(text,
		startingAtLine !== undefined ?
			fileContents.split('\n').slice(0, startingAtLine).join('\n').length // num characters in all lines before startingAtLine
			: 0
	);
	if (idx === -1) return 'Not found' as const;
	const lastIdx = fileContents.lastIndexOf(text);
	if (lastIdx !== idx) return 'Not unique' as const;
	const startLine = fileContents.substring(0, idx).split('\n').length;
	const numLines = text.split('\n').length;
	const endLine = startLine + numLines - 1;
	return [startLine, endLine] as const;
};


export type URIStreamState = 'idle' | 'acceptRejectAll' | 'streaming';


export type StartApplyingOpts = {
	from: 'QuickEdit';
	type: 'rewrite';
	diffareaid: number; // id of the CtrlK area (contains text selection)
	chatId: string;
	sessionId: string;
	businessEvent: string;
} | {
	from: 'ClickApply';
	type: 'searchReplace' | 'rewrite';
	applyStr: string;
	uri: URI;
	applyBoxId?: string;
	chatId: string;
	sessionId: string;
	businessEvent: string;
} | {
	from: 'QuickEdit';
	type: 'explain';
	instructions: string;
	uri: URI;
	startLine: number;
	endLine: number;
	chatId: string;
	sessionId: string;
	businessEvent: string;
} | {
	from: 'QuickEdit';
	type: 'refactor';
	instructions: string;
	uri: URI;
	startLine: number;
	endLine: number;
	chatId: string;
	sessionId: string;
	businessEvent: string;
};



export type AddCtrlKOpts = {
	startLine: number;
	endLine: number;
	editor: ICodeEditor;
};

export type AddTipOpts = {
	tipArea: TipArea;
	editor: ICodeEditor;
};


export type Diff = {
	diffid: number;
	diffareaid: number; // the diff area this diff belongs to, "computed"
	applyId: string;
	sessionId: string;
} & ComputedDiff;


type CommonZoneProps = {
	diffareaid: number;
	startLine: number;
	endLine: number;

	_URI: URI; // typically we get the URI from model
	applyId: string;
	sessionId: string;
	businessEvent: string;
};

export type BlankLineTipZone = {
	type: 'BlankLineTipZone';
	lineNumber: number;
	column: number;
	showPosition: {
		leftPx: number;
		topPx: number;
	};
};

type TextSelectionTipZone = {
	type: 'TextSelectionTipZone';
	startLine: number;
	endLine: number;
};


type CtrlKZone = {
	type: 'CtrlKZone';
	originalCode?: undefined;

	editorId: string; // the editor the input lives on

	_mountInfo: null | {
		textAreaRef: { current: HTMLTextAreaElement | null };
		selections: { current: StagingSelectionItem[] }
		dispose: () => void;
		refresh: () => void;
	};

	_linkedStreamingDiffZone: number | null; // diffareaid of the diffZone currently streaming here
	_removeStylesFns: Set<Function>; // these don't remove diffs or this diffArea, only their styles

} & CommonZoneProps;


type DiffZone = {
	type: 'DiffZone';
	originalCode: string;
	_diffOfId: Record<string, Diff>; // diffid -> diff in this DiffArea
	_streamState: {
		isStreaming: true;
		streamRequestIdRef: { current: string | null };
		line: number;
		applyBoxId?: string;
	} | {
		isStreaming: false;
		streamRequestIdRef?: undefined;
		line?: undefined;
		applyBoxId?: string;
	};
	editorId?: undefined;
	linkedStreamingDiffZone?: undefined;
	_removeStylesFns: Set<Function>; // these don't remove diffs or this diffArea, only their styles
} & CommonZoneProps;



type TrackingZone<T> = {
	type: 'TrackingZone';
	metadata: T;
	originalCode?: undefined;
	editorId?: undefined;
	_removeStylesFns?: undefined;
} & CommonZoneProps;


// called DiffArea for historical purposes, we can rename to something like TextRegion if we want
type DiffArea = CtrlKZone | DiffZone | TrackingZone<any>;
type TipArea = BlankLineTipZone | TextSelectionTipZone;

const diffAreaSnapshotKeys = [
	'type',
	'diffareaid',
	'originalCode',
	'startLine',
	'endLine',
	'editorId',
	'applyId',
	'sessionId',
	'businessEvent',
] as const satisfies (keyof DiffArea)[];

type DiffAreaSnapshot<DiffAreaType extends DiffArea = DiffArea> = Pick<DiffAreaType, typeof diffAreaSnapshotKeys[number]>;



type HistorySnapshot = {
	snapshottedDiffAreaOfId: Record<string, DiffAreaSnapshot>;
	entireFileCode: string;
};

const tipTypes = ['BlankLineTipZone', 'TextSelectionTipZone'] as const;



// line/col is the location, originalCodeStartLine is the start line of the original code being displayed
type StreamLocationMutable = { line: number; col: number; addedSplitYet: boolean; originalCodeStartLine: number };


export interface IEditCodeService {
	readonly _serviceBrand: undefined;
	startApplying(opts: StartApplyingOpts): Promise<URI | null>;

	addCtrlKZone(opts: AddCtrlKOpts): number | undefined;
	addTipZone(opts: AddTipOpts): void;
	removeCtrlKZone(opts: { diffareaid: number }): void;
	removeDiffAreas(opts: { uri: URI; removeCtrlKs: boolean; behavior: 'reject' | 'accept'; clearOriginalCache?: boolean }): void;
	disposeTip(type?: (typeof tipTypes)[number]): void;

	// CtrlKZone streaming state
	isCtrlKZoneStreaming(opts: { diffareaid: number }): boolean;
	interruptCtrlKStreaming(opts: { diffareaid: number }): void;
	onDidChangeCtrlKZoneStreaming: Event<{ uri: URI; diffareaid: number }>;

	// // DiffZone codeBoxId streaming state
	getURIStreamState(opts: { uri: URI | null, applyBoxId?: string }): URIStreamState;
	interruptURIStreaming(opts: { uri: URI }): void;
	onDidChangeURIStreamState: Event<{ uri: URI; state: URIStreamState }>;
	onDidChangeDiffCount: Event<{ uri: URI; currentIndex: number; totalDiffs: number }>;
	// Apply状态事件
	onDidStartApplying: Event<{ uri: URI; applyId: string }>;
	onDidEndApplying: Event<{ uri: URI; applyId: string }>;
	openOrCreateFile(uri: URI, isBackground: boolean, disableCreate?: boolean): Promise<void>;
	acceptDiffAtIndex(opts: { uri: URI; index: number }): void;
	rejectDiffAtIndex(opts: { uri: URI; index: number }): void;
	getCurrentDiffIndex(): number;
	shouldDeleteFileOnReject(uri: URI): Promise<void>;
	applyingURIOfApplyBoxIdRef: { current: { [applyBoxId: string]: URI | undefined } };
	createFilePaths: Set<string>;
}

export const IEditCodeService = createDecorator<IEditCodeService>('editCodeService');

class EditCodeService extends Disposable implements IEditCodeService {
	_serviceBrand: undefined;

	// URI <--> model
	diffAreasOfURI: Record<string, Set<string>> = {};

	diffAreaOfId: Record<string, DiffArea> = {};
	diffOfId: Record<string, Diff> = {}; // redundant with diffArea._diffs

	createFilePaths = new Set<string>();

	// 保存每个文件在第一次apply前的原始内容
	private originalFileContentBeforeAnyApply: Map<string, string> = new Map();

	// only applies to diffZones
	// streamingDiffZones: Set<number> = new Set()
	private readonly _onDidChangeDiffZoneStreaming = new Emitter<{ uri: URI; diffareaid: number; originCode?: string }>();
	private readonly _onDidAddOrDeleteDiffZones = new Emitter<{ uri: URI; originCode?: string }>();

	private readonly _onDidChangeCtrlKZoneStreaming = new Emitter<{ uri: URI; diffareaid: number }>();
	onDidChangeCtrlKZoneStreaming = this._onDidChangeCtrlKZoneStreaming.event;

	private readonly _onDidChangeURIStreamState = new Emitter<{ uri: URI; state: URIStreamState }>();
	onDidChangeURIStreamState = this._onDidChangeURIStreamState.event;

	private tips: (BlankLineTipWidget | TextSelectionTipWidget)[] = [];

	// 在类定义部分添加新的事件发射器
	private readonly _onDidChangeDiffCount = new Emitter<{ uri: URI; currentIndex: number; totalDiffs: number }>();
	onDidChangeDiffCount = this._onDidChangeDiffCount.event;

	// 存储排序后的差异
	private _sortedDiffs: Record<string, Diff[]> = {};
	// 当前的diff索引
	private _currentDiffIndex: number = 0;

	// state persisted for duration of react only
	applyingURIOfApplyBoxIdRef: { current: { [applyBoxId: string]: URI | undefined } } = { current: {} }

	// Apply状态通知事件
	private readonly _onDidStartApplying = new Emitter<{ uri: URI; applyId: string }>();
	readonly onDidStartApplying = this._onDidStartApplying.event;

	private readonly _onDidEndApplying = new Emitter<{ uri: URI; applyId: string }>();
	readonly onDidEndApplying = this._onDidEndApplying.event;


	constructor(
		@ICodeEditorService private readonly _codeEditorService: ICodeEditorService,
		@IModelService private readonly _modelService: IModelService,
		@ITextModelService private readonly _textModelService: ITextModelService,
		@IUndoRedoService private readonly _undoRedoService: IUndoRedoService, // undoRedo service is the history of pressing ctrl+z
		@ILLMMessageService private readonly _llmMessageService: ILLMMessageService,
		@IConsistentItemService private readonly _consistentItemService: IConsistentItemService,
		@IInstantiationService private readonly _instantiationService: IInstantiationService,
		@IConsistentEditorItemService private readonly _consistentEditorItemService: IConsistentEditorItemService,
		@IMetricsService private readonly _metricsService: IMetricsService,
		@INotificationService private readonly _notificationService: INotificationService,
		@ICommandService private readonly _commandService: ICommandService,
		@ICodeseekFileService private readonly _codeseekFileService: ICodeseekFileService,
		@ISidebarStateService private readonly _sidebarStateService: ISidebarStateService,
		@IQuickEditStateService private readonly _quickEditStateService: IQuickEditStateService,
		@ICodeseekLogger private readonly _codeseekLogService: ICodeseekLogger,
		@ILifecycleService private readonly _lifecycleService: ILifecycleService,
		@IFileService private readonly _fileService: IFileService,
		@IWorkspaceContextService private readonly _workspaceService: IWorkspaceContextService,
		@IEditorService private readonly _editorService: IEditorService,
		@ICodeSeekExporerService private readonly _codeseekExporerService: ICodeSeekExporerService,
		@ICodeseekSettingsService private readonly _codeseekSettingsService: ICodeseekSettingsService,
		@IContextKeyService private readonly _contextKeyService: IContextKeyService,
		@ICodeseekUacLoginService private readonly _codeseekUacLoginService: ICodeseekUacLoginService,
		@IWorkingCopyService private readonly _workingCopyService: IWorkingCopyService,
		@ILargeFileApplyService private readonly _largeFileApplyService: ILargeFileApplyService,
	) {
		super();

		// this function initializes data structures and listens for changes
		const initializeModel = (model: ITextModel) => {
			if (!(model.uri.fsPath in this.diffAreasOfURI)) {
				this.diffAreasOfURI[model.uri.fsPath] = new Set();
				this._sortedDiffs[model.uri.fsPath] = [];
			}
			else return; // do not add listeners to the same model twice - important, or will see duplicates

			// when the user types, realign diff areas and re-render them
			this._register(
				model.onDidChangeContent(e => {
					// it's as if we just called _write, now all we need to do is realign and refresh
					if (this.weAreWriting) return;
					const uri = model.uri;
					this._onUserChangeContent(uri, e);
				})
			);

			// when a stream starts or ends, fire the event for onDidChangeURIStreamState
			let prevStreamState = this.getURIStreamState({ uri: model.uri });
			const updateAcceptRejectAllUI = () => {
				const state = this.getURIStreamState({ uri: model.uri });
				const prevStateActual = prevStreamState;
				prevStreamState = state;
				if (state === prevStateActual) return;
				this._onDidChangeURIStreamState.fire({ uri: model.uri, state });
			};


			let _removeAcceptRejectAllUI: (() => void) | null = null;
			this._register(this._onDidChangeURIStreamState.event(({ uri, state }) => {
				if (uri.fsPath !== model.uri.fsPath) return;
				if (state === 'acceptRejectAll') {
					if (!_removeAcceptRejectAllUI)
						_removeAcceptRejectAllUI = this._addAcceptRejectAllUI(model.uri) ?? null;
				} else {
					_removeAcceptRejectAllUI?.();
					_removeAcceptRejectAllUI = null;
				}
			}));
			this._register(this._onDidChangeDiffZoneStreaming.event(({ uri: uri_, originCode }) => {
				if (uri_.fsPath === model.uri.fsPath) {
					if (originCode ? model.getValue() !== originCode : true) {
						updateAcceptRejectAllUI();
					} else {
						this.removeDiffAreas({ uri: model.uri, behavior: 'reject', removeCtrlKs: false, clearOriginalCache: false });
					}
				}
			}));
			this._register(this._onDidAddOrDeleteDiffZones.event(({ uri: uri_, originCode }) => {
				if (uri_.fsPath === model.uri.fsPath) {
					if (originCode ? model.getValue() !== originCode : true) {
						updateAcceptRejectAllUI();
					} else {
						this.removeDiffAreas({ uri: model.uri, behavior: 'reject', removeCtrlKs: false, clearOriginalCache: false });
					}
				}
			}));


		};
		// initialize all existing models + initialize when a new model mounts
		for (const model of this._modelService.getModels()) { initializeModel(model); }
		this._register(this._modelService.onModelAdded(model => initializeModel(model)));
		// 当模型被移除时，清理该URI的所有diff区域
		this._register(this._modelService.onModelRemoved(model => {
			const uri = model.uri;
			// this.clearAllDiffAreasInEditor(uri);
			this._lastRefreshTimeMap.delete(uri.toString());
		}));



		// this function adds listeners to refresh styles when editor changes tab
		const initializeEditor = (editor: ICodeEditor) => {
			const uri = editor.getModel()?.uri ?? null;
			if (uri) this._refreshStylesAndDiffsInURI(uri, true);
		};
		// add listeners for all existing editors + listen for editor being added
		for (const editor of this._codeEditorService.listCodeEditors()) { initializeEditor(editor); }
		this._register(this._codeEditorService.onCodeEditorAdd(editor => { initializeEditor(editor); }));

		// 监听应用关闭事件，清理所有diff区域
		this._register(this._lifecycleService.onWillShutdown(() => {
			this.clearAllDiffAreasInWorkspace();
		}));
	}

	private clearAllDiffAreasInEditor(uri: URI): void {
		if (this.diffAreasOfURI[uri.fsPath]?.size > 0) {
			this._codeseekLogService.info(`the editor is closed, clear the diff areas: ${uri.fsPath}`);
			this._deleteAllDiffAreas(uri);
			this.diffAreasOfURI[uri.fsPath].clear();
			this._onDidAddOrDeleteDiffZones.fire({ uri });
		}
		// 注意：不清除原始文件内容缓存，因为用户可能重新打开文件继续查看累积的diff
	}

	/**
	 * 清理工作区中所有URI的diff区域
	 */
	private clearAllDiffAreasInWorkspace(): void {
		// 获取所有URI并清理相关diff区域
		for (const uriPath in this.diffAreasOfURI) {
			if (this.diffAreasOfURI[uriPath]?.size > 0) {
				this.clearAllDiffAreasInEditor(URI.parse(uriPath));
			}
		}
	}


	private _onUserChangeContent(uri: URI, e: IModelContentChangedEvent) {
		for (const change of e.changes) {
			this._realignAllDiffAreasLines(uri, change.text, change.range);
		}
		// 对用户手动修改使用强制刷新，绕过节流机制，确保立即显示新的diff
		this._refreshStylesAndDiffsInURI(uri, true);
	}

	private _onInternalChangeContent(uri: URI, { shouldRealign }: { shouldRealign: false | { newText: string; oldRange: IRange } }) {
		if (shouldRealign) {
			const { newText, oldRange } = shouldRealign;
			// console.log('realiging', newText, oldRange)
			this._realignAllDiffAreasLines(uri, newText, oldRange);
		}
		this._refreshStylesAndDiffsInURI(uri);

	}

	private _notifyError = (e: Parameters<OnError>[0]) => {
		const details = errorDetails(e.fullError);
		this._notificationService.notify({
			severity: Severity.Warning,
			message: `Flow Error: ${e.message}`,
			actions: {
				secondary: [{
					id: 'codeseek.onerror.opensettings',
					enabled: true,
					label: `Open flow settings`,
					tooltip: '',
					class: undefined,
					run: () => { this._commandService.executeCommand(CODESEEK_OPEN_SETTINGS_ACTION_ID); }
				}]
			},
			source: details ? `(Hold ${isMacintosh ? 'Option' : 'Alt'} to hover) - ${details}\n\nIf this persists, feel free to [report](https://github.com/codeseekeditor/codeseek/issues/new) it.` : undefined
		});
	};



	// highlight the region
	private _addLineDecoration = (model: ITextModel | null, startLine: number, endLine: number, className: string, options?: Partial<IModelDecorationOptions>) => {
		if (model === null) return;
		const id = model.changeDecorations(accessor => accessor.addDecoration(
			{ startLineNumber: startLine, startColumn: 1, endLineNumber: endLine, endColumn: Number.MAX_SAFE_INTEGER },
			{
				className: className,
				description: className,
				isWholeLine: true,
				...options
			}));
		const disposeHighlight = () => {
			if (id && !model.isDisposed()) model.changeDecorations(accessor => accessor.removeDecoration(id));
		};
		return disposeHighlight;
	};


	private _addDiffAreaStylesToURI = (uri: URI) => {
		const model = this._getModel(uri);

		for (const diffareaid of this.diffAreasOfURI[uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (!diffArea) continue;
			if (diffArea.type === 'DiffZone') {
				// add sweep styles to the diffZone
				if (diffArea._streamState.isStreaming) {
					// sweepLine ... sweepLine
					const fn1 = this._addLineDecoration(model, diffArea._streamState.line, diffArea._streamState.line, 'codeseek-sweepIdxBG');
					// sweepLine+1 ... endLine
					const fn2 = diffArea._streamState.line + 1 <= diffArea.endLine ?
						this._addLineDecoration(model, diffArea._streamState.line + 1, diffArea.endLine, 'codeseek-sweepBG')
						: null;
					diffArea._removeStylesFns.add(() => { fn1?.(); fn2?.(); });
				}
			}

			else if (diffArea.type === 'CtrlKZone' && diffArea._linkedStreamingDiffZone === null) {
				// highlight zone's text
				const fn = this._addLineDecoration(model, diffArea.startLine, diffArea.endLine, 'codeseek-highlightBG');
				diffArea._removeStylesFns.add(() => fn?.());
			}
		}
	};


	private _computeDiffsAndAddStylesToURI = (uri: URI) => {
		const fullFileText = this._readURI(uri) ?? '';

		for (const diffareaid of this.diffAreasOfURI[uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (!diffArea) continue;
			if (diffArea.type !== 'DiffZone') continue;

			const newDiffAreaCode = fullFileText.split('\n').slice((diffArea.startLine - 1), (diffArea.endLine - 1) + 1).join('\n');
			const computedDiffs = findDiffs(diffArea.originalCode, newDiffAreaCode);
			for (const computedDiff of computedDiffs) {
				if (computedDiff.type === 'deletion') {
					computedDiff.startLine += diffArea.startLine - 1;
				}
				if (computedDiff.type === 'edit' || computedDiff.type === 'insertion') {
					computedDiff.startLine += diffArea.startLine - 1;
					computedDiff.endLine += diffArea.startLine - 1;
				}
				this._addDiff(computedDiff, diffArea);
			}
			this._sortedDiffs[uri.fsPath].sort((a, b) => a.startLine - b.startLine);
		}

		// 触发事件通知，确保UI状态更新
		if (Object.keys(this.diffAreasOfURI[uri.fsPath] || {}).length > 0) {
			this._onDidAddOrDeleteDiffZones.fire({ uri });
		}
	};



	private _addAcceptRejectAllUI(uri: URI) {
		if (!this._sortedDiffs[uri.fsPath] || this._sortedDiffs[uri.fsPath].length === 0) {
			return;
		}

		// 检查是否有其他文件包含diff区域
		const consistentItemId = this._consistentItemService.addConsistentItemToURI({
			uri,
			fn: (editor) => {
				const buttonsWidget = new AcceptAllRejectAllWidget({
					editor,
					onAcceptAll: () => {
						this.removeDiffAreas({ uri, behavior: 'accept', removeCtrlKs: false });
						this.createFilePaths.delete(uri.fsPath);
						// this._metricsService.capture('Accept All', {});
					},
					onRejectAll: () => {
						this.removeDiffAreas({ uri, behavior: 'reject', removeCtrlKs: false });
						this.shouldDeleteFileOnReject(uri);
						// this._metricsService.capture('Reject All', {});
					},
					onNextDiff: (step: number) => {
						const result = this._navigateToNextDiff(uri, editor, step);
						if (result) {
							buttonsWidget.updateDiffCounter(result.currentIndex, result.totalDiffs);
						}
						// this._metricsService.capture('Navigate Diff', { step });
					},
					onNextFile: () => {
						this._navigateToNextFileWithDiffs(uri);
						// this._metricsService.capture('Navigate Next File', {});
					},
					hasNextFile: () => {
						return this._hasNextFileWithDiffs(uri);
					},
					onDidChangeDiffCount: this.onDidChangeDiffCount
				});

				setTimeout(() => {
					this._updateDiffCount(uri);
				}, 0);

				return () => { buttonsWidget.dispose(); };
			}
		});

		return () => { this._consistentItemService.removeConsistentItemFromURI(consistentItemId); };
	}

	/**
	 * 检查是否有其他文件包含diff区域
	 * @param currentUri 当前文件URI
	 * @returns 是否有其他文件包含diff区域
	 */
	private _hasNextFileWithDiffs(currentUri: URI): boolean {
		for (const uriPath in this.diffAreasOfURI) {
			if (uriPath === currentUri.fsPath) continue;

			const diffAreas = this.diffAreasOfURI[uriPath];
			if (!diffAreas || diffAreas.size === 0) continue;

			// 检查是否有非流式传输的DiffZone
			for (const diffareaid of diffAreas) {
				const diffArea = this.diffAreaOfId[diffareaid];
				if (diffArea?.type === 'DiffZone' && !diffArea._streamState.isStreaming) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 导航到下一个包含diff区域的文件
	 * @param currentUri 当前文件URI
	 */
	private _navigateToNextFileWithDiffs(currentUri: URI): void {
		// 收集所有包含diff的文件URI
		const urisWithDiffs: URI[] = [];
		for (const uriPath in this.diffAreasOfURI) {
			if (uriPath === currentUri.fsPath) continue;

			const diffAreas = this.diffAreasOfURI[uriPath];
			if (!diffAreas || diffAreas.size === 0) continue;

			// 检查是否有非流式传输的DiffZone
			let hasDiffZone = false;
			for (const diffareaid of diffAreas) {
				const diffArea = this.diffAreaOfId[diffareaid];
				if (diffArea?.type === 'DiffZone' && !diffArea._streamState.isStreaming) {
					hasDiffZone = true;
					break;
				}
			}

			if (hasDiffZone) {
				urisWithDiffs.push(URI.parse(uriPath));
			}
		}

		if (urisWithDiffs.length === 0) return;

		// 打开第一个包含diff的文件
		this._editorService.openEditor({ resource: urisWithDiffs[0] });
	}

	// 导航到下一个或上一个差异
	private _navigateToNextDiff(uri: URI, editor: ICodeEditor, step: number): { currentIndex: number; totalDiffs: number } | null {
		// 直接使用已排序的差异数组
		const allDiffs = this._sortedDiffs[uri.fsPath];
		if (!allDiffs || allDiffs.length === 0) {
			return null;
		}

		// 获取当前光标位置
		const position = editor.getPosition();
		if (!position) return null;

		// 查找当前位置之后/之前的下一个差异
		let targetDiff: Diff | undefined;

		if (step > 0) { // 向下导航
			targetDiff = allDiffs.find(diff => diff.startLine > position.lineNumber);
			if (!targetDiff && allDiffs.length > 0) {
				// 如果没有找到，则循环到第一个差异
				targetDiff = allDiffs[0];
			}
		} else { // 向上导航
			// 反向查找
			for (let i = allDiffs.length - 1; i >= 0; i--) {
				const diff = allDiffs[i];
				// 使用startLine作为比较点，避免类型问题
				if (diff.startLine < position.lineNumber) {
					targetDiff = diff;
					break;
				}
			}
			if (!targetDiff && allDiffs.length > 0) {
				// 如果没有找到，则循环到最后一个差异
				targetDiff = allDiffs[allDiffs.length - 1];
			}
		}

		// 如果找到目标差异，则滚动到该位置
		if (targetDiff) {
			editor.revealLineInCenter(targetDiff.startLine, ScrollType.Smooth);
			editor.setPosition({ lineNumber: targetDiff.startLine, column: 1 });
			this._currentDiffIndex = allDiffs.indexOf(targetDiff);
		}

		const result = { currentIndex: this._currentDiffIndex, totalDiffs: allDiffs.length };

		// 触发事件
		this._onDidChangeDiffCount.fire({ uri, currentIndex: result.currentIndex, totalDiffs: result.totalDiffs });

		return result;
	}

	mostRecentTextOfCtrlKZoneId: Record<string, string | undefined> = {};
	mostSelectionsOfCtrlKZoneId: Record<string, StagingSelectionItem[] | undefined> = {}
	private _addCtrlKZoneInput = (ctrlKZone: CtrlKZone) => {
		const { editorId } = ctrlKZone;
		const editor = this._codeEditorService.listCodeEditors().find(e => e.getId() === editorId);
		if (!editor) { return null; }

		let zoneId: string | null = null;
		let viewZone_: IViewZone | null = null;
		const textAreaRef: { current: HTMLTextAreaElement | null } = { current: null };
		const stagingSelections: { current: StagingSelectionItem[] } = { current: [] }


		const paddingLeft = getLeadingWhitespacePx(editor, ctrlKZone.startLine);

		const itemId = this._consistentEditorItemService.addToEditor(editor, () => {
			const domNode = document.createElement('div');
			domNode.style.zIndex = '1';
			domNode.style.height = 'auto';
			domNode.style.paddingLeft = `${paddingLeft}px`;
			const viewZone: IViewZone = {
				afterLineNumber: ctrlKZone.startLine - 1,
				domNode: domNode,
				// heightInPx: 80,
				suppressMouseDown: false,
				showInHiddenAreas: true,
			};
			viewZone_ = viewZone;

			// mount zone
			editor.changeViewZones(accessor => {
				zoneId = accessor.addZone(viewZone);
			});

			// mount react
			this._instantiationService.invokeFunction(accessor => {
				mountCtrlK(domNode, accessor, {

					diffareaid: ctrlKZone.diffareaid,

					textAreaRef: (r) => {
						textAreaRef.current = r;
						if (!textAreaRef.current) return;

						if (!(ctrlKZone.diffareaid in this.mostRecentTextOfCtrlKZoneId)) { // detect first mount this way (a hack)
							this.mostRecentTextOfCtrlKZoneId[ctrlKZone.diffareaid] = undefined;
							setTimeout(() => textAreaRef.current?.focus(), 100);
						}
					},
					onChangeHeight(height) {
						if (height === 0) return; // the viewZone sets this height to the container if it's out of view, ignore it
						viewZone.heightInPx = height;
						// re-render with this new height
						editor.changeViewZones(accessor => {
							if (zoneId) accessor.layoutZone(zoneId);
						});
					},
					onChangeText: (text) => {
						this.mostRecentTextOfCtrlKZoneId[ctrlKZone.diffareaid] = text;
					},
					onClose: () => {
						this._quickEditStateService.setState({ isOpenEdit: false });
						this._quickEditStateService.fireChangeState();
					},
					setSelections: (selections) => {
						this.mostSelectionsOfCtrlKZoneId[ctrlKZone.diffareaid] = [...selections]
						stagingSelections.current = [...selections]
					},
					selections: this.mostSelectionsOfCtrlKZoneId[ctrlKZone.diffareaid] ?? [],
					initText: this.mostRecentTextOfCtrlKZoneId[ctrlKZone.diffareaid] ?? null,
				} satisfies QuickEditPropsType);

			});

			return () => editor.changeViewZones(accessor => {
				if (zoneId)
					accessor.removeZone(zoneId);
			});
		});

		return {
			textAreaRef,
			selections: stagingSelections,
			refresh: () => editor.changeViewZones(accessor => {
				if (zoneId && viewZone_) {
					viewZone_.afterLineNumber = ctrlKZone.startLine - 1;
					accessor.layoutZone(zoneId);
				}
			}),
			dispose: () => {
				this._consistentEditorItemService.removeFromEditor(itemId);
			},
		} satisfies CtrlKZone['_mountInfo'];
	};

	private _refreshCtrlKInputs = async (uri: URI) => {
		for (const diffareaid of this.diffAreasOfURI[uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (!diffArea) continue;
			if (diffArea.type !== 'CtrlKZone') continue;
			if (!diffArea._mountInfo) {
				diffArea._mountInfo = this._addCtrlKZoneInput(diffArea);
			}
			else {
				diffArea._mountInfo.refresh();
			}
		}
	};

	private _addDiffStylesToURI = (uri: URI, diff: Diff) => {
		const { type, diffid } = diff;

		const disposeInThisEditorFns: (() => void)[] = [];

		// green decoration and minimap decoration
		if (type !== 'deletion') {
			const consistentDecorationId = this._consistentItemService.addConsistentItemToURI({
				uri,
				fn: (editor) => {
					const model = editor.getModel();
					if (!model) {
						return () => { };
					}
					const fn = this._addLineDecoration(model, diff.startLine, diff.endLine, 'codeseek-greenBG', {
						minimap: { color: { id: 'minimapGutter.addedBackground' }, position: 2 },
						overviewRuler: { color: { id: 'editorOverviewRuler.addedForeground' }, position: 7 }
					});
					return () => { fn?.(); };
				}
			});
			disposeInThisEditorFns.push(() => { this._consistentItemService.removeConsistentItemFromURI(consistentDecorationId); });
		}


		// red in a view zone
		if (type !== 'insertion') {
			const consistentZoneId = this._consistentItemService.addConsistentItemToURI({
				uri,
				fn: (editor) => {

					const domNode = document.createElement('div');
					domNode.className = 'codeseek-redBG';

					const renderOptions = RenderOptions.fromEditor(editor);

					const processedText = diff.originalCode.replace(/\t/g, ' '.repeat(renderOptions.tabSize));

					const lines = processedText.split('\n');

					const linesContainer = document.createElement('div');
					linesContainer.style.fontFamily = renderOptions.fontInfo.fontFamily;
					linesContainer.style.fontSize = `${renderOptions.fontInfo.fontSize}px`;
					linesContainer.style.lineHeight = `${renderOptions.fontInfo.lineHeight}px`;
					// linesContainer.style.tabSize = `${tabWidth}px` // \t
					linesContainer.style.whiteSpace = 'pre';
					linesContainer.style.position = 'relative';
					linesContainer.style.width = '100%';

					lines.forEach(line => {
						// div for current line
						const lineDiv = document.createElement('div');
						lineDiv.className = 'view-line';
						lineDiv.style.whiteSpace = 'pre';
						lineDiv.style.position = 'relative';
						lineDiv.style.height = `${renderOptions.fontInfo.lineHeight}px`;

						// span (this is just how vscode does it)
						const span = document.createElement('span');
						span.textContent = line || '\u00a0';
						span.style.whiteSpace = 'pre';
						span.style.display = 'inline-block';

						lineDiv.appendChild(span);
						linesContainer.appendChild(lineDiv);
					});

					domNode.appendChild(linesContainer);

					// Calculate height based on number of lines and line height
					const heightInLines = lines.length;
					const minWidthInPx = Math.max(...lines.map(line =>
						Math.ceil(renderOptions.fontInfo.typicalFullwidthCharacterWidth * line.length)
					));

					const viewZone: IViewZone = {
						afterLineNumber: diff.startLine - 1,
						heightInLines,
						minWidthInPx,
						domNode,
						marginDomNode: document.createElement('div'),
						suppressMouseDown: false,
						showInHiddenAreas: false,
					};

					let zoneId: string | null = null;
					editor.changeViewZones(accessor => { zoneId = accessor.addZone(viewZone); });
					return () => editor.changeViewZones(accessor => { if (zoneId) accessor.removeZone(zoneId); });
				},
			});

			disposeInThisEditorFns.push(() => { this._consistentItemService.removeConsistentItemFromURI(consistentZoneId); });

		}



		const diffZone = this.diffAreaOfId[diff.diffareaid];
		if (!diffZone) {
			this._codeseekLogService.warn(`diffZone not found for diffareaid ${diff.diffareaid} in _addDiffStylesToURI`);
			return;
		}
		if (diffZone.type === 'DiffZone' && !diffZone._streamState.isStreaming) {
			// Accept | Reject widget - 每个diff都有自己的按钮
			const consistentWidgetId = this._consistentItemService.addConsistentItemToURI({
				uri,
				fn: (editor) => {
					let startLine: number;
					let endLine: number;
					let offsetLines: number;
					if (diff.type === 'insertion' || diff.type === 'edit') {
						startLine = diff.startLine; // green start
						endLine = diff.endLine;
						offsetLines = 0;
					}
					else if (diff.type === 'deletion') {
						// 对于deletion，viewzone创建在afterLineNumber: diff.startLine - 1
						// 按钮应该显示在viewzone的底部，也就是紧接着diff.startLine的位置
						startLine = diff.startLine;
						endLine = diff.startLine; // deletion在当前文件中没有endLine，使用startLine

						// deletion按钮不需要额外偏移，直接显示在startLine位置
						offsetLines = 0;
					}
					else { throw 1; }

					const buttonsWidget = new AcceptRejectWidget({
						editor,
						onAccept: () => {
							this.acceptDiff({ diffid });
							this.createFilePaths.delete(uri.fsPath);
							// this._metricsService.capture('Accept Diff', {});
						},
						onReject: async () => {
							this.rejectDiff({ diffid });
							// 读取编辑器内存中的最新内容，而不是磁盘文件
							const fileContent = this._readURI(uri);
							if (fileContent !== null && fileContent.trim() === '') {
								await this.shouldDeleteFileOnReject(uri);
							}
						},
						diffid: diffid.toString(),
						startLine,
						endLine,
						offsetLines,
						diffType: diff.type
					});
					return () => { buttonsWidget.dispose(); };
				}
			});
			disposeInThisEditorFns.push(() => { this._consistentItemService.removeConsistentItemFromURI(consistentWidgetId); });
		}

		const disposeInEditor = () => { disposeInThisEditorFns.forEach(f => f()); };
		return disposeInEditor;

	};

	public async shouldDeleteFileOnReject(uri: URI) {
		if (this.createFilePaths.has(uri.fsPath)) {
			try {
				// 先检查文件是否有未保存的更改
				const workingCopies = this._workingCopyService.workingCopies.filter(copy =>
					copy.resource.toString() === uri.toString());

				// 如果有未保存的更改，先放弃这些更改
				if (workingCopies.length > 0) {
					this._codeseekLogService.info(`Discarding unsaved changes for: ${uri.fsPath}`);
					await Promise.all(workingCopies.map(copy => copy.revert({ soft: false })));
				}

				// 关闭编辑器
				const editorsToClose = this._editorService.findEditors(uri);
				if (editorsToClose.length > 0) {
					this._codeseekLogService.info(`Closing editors for file before deletion: ${uri.fsPath}`);
					await this._editorService.closeEditors(editorsToClose, { preserveFocus: true });
				}

				// 检查是否可以删除文件
				const canDelete = await this._fileService.canDelete(uri, { useTrash: true, recursive: false });
				if (canDelete instanceof Error) {
					throw canDelete;
				}

				// 删除文件
				await this._fileService.del(uri, { useTrash: true, recursive: false });
				this.createFilePaths.delete(uri.fsPath);
				this._codeseekLogService.info(`Successfully deleted file: ${uri.fsPath}`);
			} catch (error) {
				this._codeseekLogService.error(`Failed to delete file: ${uri.fsPath}`, error);
			}
		}
	}


	public async openOrCreateFile(uri: URI, isBackground: boolean, disableCreate?: boolean): Promise<void> {
		try {
			const { workspaceUri } = getWorkspaceUri(this._workspaceService);
			const isInWorkspace = workspaceUri && uri.fsPath.startsWith(workspaceUri.fsPath);
			const exists = await this._fileService.exists(uri);

			// 如果文件不存在并且在工作区中，弹框让用户授权创建文件
			if (!exists && isInWorkspace) {
				if (!disableCreate) {
					await this._fileService.createFile(uri);
					this.createFilePaths.add(uri.fsPath);
				} else {
					return
				}
			}
			// 默认打开但不激活当前编辑器，且固定标签（非预览）
			await this._editorService.openEditor({
				resource: uri,
				options: {
					pinned: true,                 // 等价 preview: false
					inactive: isBackground,               // 等价 background: true
					preserveFocus: isBackground,
					activation: EditorActivation.PRESERVE
				}
			});
		} catch (error) {
			this._codeseekLogService.error("Failed to open or create file:", error);
		}
	};

	private _getModel(uri: URI) {
		const model = this._modelService.getModel(uri);
		if (!model || model.isDisposed()) {
			return null;
		}
		return model;
	}
	private _readURI(uri: URI, range?: IRange): string | null {
		if (!range) return this._getModel(uri)?.getValue(EndOfLinePreference.LF) ?? null;
		else return this._getModel(uri)?.getValueInRange(range, EndOfLinePreference.LF) ?? null;
	}
	private _getNumLines(uri: URI): number | null {
		return this._getModel(uri)?.getLineCount() ?? null;
	}
	private _getActiveEditorURI(): URI | null {
		const editor = this._codeEditorService.getActiveCodeEditor();
		if (!editor) return null;
		const uri = editor.getModel()?.uri;
		if (!uri) return null;
		return uri;
	}

	weAreWriting = false;
	private _writeText(uri: URI, text: string, range: IRange, { shouldRealignDiffAreas }: { shouldRealignDiffAreas: boolean }) {
		const model = this._getModel(uri);
		if (!model) return;
		const uriStr = this._readURI(uri, range);
		if (uriStr === null) return;


		// heuristic check if don't need to make edits
		const dontNeedToWrite = uriStr === text;
		if (dontNeedToWrite) {
			// at the end of a write, we still expect to refresh all styles
			// e.g. sometimes we expect to restore all the decorations even if no edits were made when _writeText is used
			this._refreshStylesAndDiffsInURI(uri);
			return;
		}

		// minimal edits so not so flashy
		// const edits = this.worker.$Codeseek_computeMoreMinimalEdits(uri.toString(), [{ range, text }], false)
		this.weAreWriting = true;
		model.applyEdits([{ range, text }]);
		this.weAreWriting = false;
		this._onInternalChangeContent(uri, { shouldRealign: shouldRealignDiffAreas && { newText: text, oldRange: range } });
	}

	private _addToHistory(uri: URI) {

		const getCurrentSnapshot = (): HistorySnapshot => {
			const snapshottedDiffAreaOfId: Record<string, DiffAreaSnapshot> = {};

			for (const diffareaid in this.diffAreaOfId) {
				const diffArea = this.diffAreaOfId[diffareaid];

				if (diffArea._URI.fsPath !== uri.fsPath) continue;

				snapshottedDiffAreaOfId[diffareaid] = structuredClone( // a structured clone must be on a JSON object
					Object.fromEntries(diffAreaSnapshotKeys.map(key => [key, diffArea[key]]))
				) as DiffAreaSnapshot;
			}
			return {
				snapshottedDiffAreaOfId,
				entireFileCode: this._readURI(uri) ?? '', // the whole file's code
			};
		};

		const restoreDiffAreas = (snapshot: HistorySnapshot) => {

			// for each diffarea in this uri, stop streaming if currently streaming
			for (const diffareaid in this.diffAreaOfId) {
				const diffArea = this.diffAreaOfId[diffareaid];
				if (diffArea.type === 'DiffZone')
					this._stopIfStreaming(diffArea);
			}

			// delete all diffareas on this uri (clearing their styles)
			this._deleteAllDiffAreas(uri);
			this.diffAreasOfURI[uri.fsPath].clear();

			const { snapshottedDiffAreaOfId, entireFileCode: entireModelCode } = structuredClone(snapshot); // don't want to destroy the snapshot

			// restore diffAreaOfId and diffAreasOfModelId
			for (const diffareaid in snapshottedDiffAreaOfId) {

				const snapshottedDiffArea = snapshottedDiffAreaOfId[diffareaid];

				if (snapshottedDiffArea.type === 'DiffZone') {
					this.diffAreaOfId[diffareaid] = {
						...snapshottedDiffArea as DiffAreaSnapshot<DiffZone>,
						type: 'DiffZone',
						_diffOfId: {},
						_URI: uri,
						_streamState: { isStreaming: false }, // when restoring, we will never be streaming
						_removeStylesFns: new Set(),
					};
				}
				else if (snapshottedDiffArea.type === 'CtrlKZone') {
					this.diffAreaOfId[diffareaid] = {
						...snapshottedDiffArea as DiffAreaSnapshot<CtrlKZone>,
						_URI: uri,
						_removeStylesFns: new Set<Function>(),
						_mountInfo: null,
						_linkedStreamingDiffZone: null, // when restoring, we will never be streaming
					};
				}
				this.diffAreasOfURI[uri.fsPath].add(diffareaid);
			}
			this._onDidAddOrDeleteDiffZones.fire({ uri });

			// restore file content
			const numLines = this._getNumLines(uri);
			if (numLines === null) return;


			this._writeText(uri, entireModelCode,
				{ startColumn: 1, startLineNumber: 1, endLineNumber: numLines, endColumn: Number.MAX_SAFE_INTEGER },
				{ shouldRealignDiffAreas: false }
			);

			this._computeDiffsAndAddStylesToURI(uri);
			this._refreshStylesAndDiffsInURI(uri);
			this._updateDiffCount(uri);
			const state = this.getURIStreamState({ uri });
			this._onDidChangeURIStreamState.fire({ uri, state });
		};

		const beforeSnapshot: HistorySnapshot = getCurrentSnapshot();
		let afterSnapshot: HistorySnapshot | null = null;

		const elt: IUndoRedoElement = {
			type: UndoRedoElementType.Resource,
			resource: uri,
			label: 'Codeseek Changes',
			code: 'undoredo.editCode',
			undo: () => { restoreDiffAreas(beforeSnapshot); },
			redo: () => { if (afterSnapshot) restoreDiffAreas(afterSnapshot); }
		};
		this._undoRedoService.pushElement(elt);

		const onFinishEdit = () => { afterSnapshot = getCurrentSnapshot(); };
		return { onFinishEdit };
	}

	// delete diffOfId and diffArea._diffOfId
	private _deleteDiff(diff: Diff) {
		const diffArea = this.diffAreaOfId[diff.diffareaid];
		if (diffArea.type !== 'DiffZone') return;
		delete diffArea._diffOfId[diff.diffid];
		delete this.diffOfId[diff.diffid];

		// 直接从排序数组中移除差异
		const uri = diffArea._URI;
		if (this._sortedDiffs[uri.fsPath]) {
			const index = this._sortedDiffs[uri.fsPath].findIndex(d => d.diffid === diff.diffid);
			if (index !== -1) {
				this._sortedDiffs[uri.fsPath].splice(index, 1);
			}
		}
	}

	private _deleteDiffs(diffZone: DiffZone) {
		for (const diffid in diffZone._diffOfId) {
			const diff = diffZone._diffOfId[diffid];
			this._deleteDiff(diff);
		}
	}

	private _clearAllDiffAreaEffects(diffArea: DiffArea) {
		// clear diffZone effects (diffs)
		if (diffArea.type === 'DiffZone')
			this._deleteDiffs(diffArea);

		diffArea._removeStylesFns?.forEach(removeStyles => removeStyles());
		diffArea._removeStylesFns?.clear();
	}


	// clears all Diffs (and their styles) and all styles of DiffAreas, etc
	private _clearAllEffects(uri: URI) {
		for (const diffareaid of this.diffAreasOfURI[uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (!diffArea) continue;
			this._clearAllDiffAreaEffects(diffArea);
		}
	}


	// delete all diffs, update diffAreaOfId, update diffAreasOfModelId
	private _deleteDiffZone(diffZone: DiffZone) {
		this._clearAllDiffAreaEffects(diffZone);
		delete this.diffAreaOfId[diffZone.diffareaid];
		this.diffAreasOfURI[diffZone._URI.fsPath].delete(diffZone.diffareaid.toString());
		this._onDidAddOrDeleteDiffZones.fire({ uri: diffZone._URI });
	}

	private _deleteTrackingZone(trackingZone: TrackingZone<unknown>) {
		delete this.diffAreaOfId[trackingZone.diffareaid];
		this.diffAreasOfURI[trackingZone._URI.fsPath].delete(trackingZone.diffareaid.toString());
	}

	private _deleteCtrlKZone(ctrlKZone: CtrlKZone) {
		this._clearAllEffects(ctrlKZone._URI);
		ctrlKZone._mountInfo?.dispose();
		delete this.diffAreaOfId[ctrlKZone.diffareaid];
		this.diffAreasOfURI[ctrlKZone._URI.fsPath].delete(ctrlKZone.diffareaid.toString());
	}


	private _deleteAllDiffAreas(uri: URI) {
		const diffAreas = this.diffAreasOfURI[uri.fsPath];
		diffAreas.forEach(diffareaid => {
			const diffArea = this.diffAreaOfId[diffareaid];

			// 添加安全检查，防止数据不一致导致的错误
			if (!diffArea) {
				// 清理不一致的数据
				this.diffAreasOfURI[uri.fsPath].delete(diffareaid.toString());
				this._codeseekLogService.warn(`Found inconsistent diff area data during deletion: diffareaid ${diffareaid} exists in diffAreasOfURI but not in diffAreaOfId for URI ${uri.fsPath}`);
				return;
			}

			if (diffArea.type === 'DiffZone')
				this._deleteDiffZone(diffArea);
			else if (diffArea.type === 'CtrlKZone')
				this._deleteCtrlKZone(diffArea);
		});
	}



	private _diffareaidPool = 0; // each diffarea has an id
	private _addDiffArea<T extends DiffArea>(diffArea: Omit<T, 'diffareaid'>): T {
		const diffareaid = this._diffareaidPool++;
		const diffArea2 = { ...diffArea, diffareaid } as T;
		this.diffAreasOfURI[diffArea2._URI.fsPath].add(diffareaid.toString());
		this.diffAreaOfId[diffareaid] = diffArea2;
		return diffArea2;
	}

	private _diffidPool = 0; // each diff has an id
	private _addDiff(computedDiff: ComputedDiff, diffZone: DiffZone): Diff {
		const uri = diffZone._URI;
		const diffid = this._diffidPool++;

		// create a Diff of it
		const newDiff: Diff = {
			...computedDiff,
			diffid: diffid,
			applyId: diffZone.applyId,
			sessionId: diffZone.sessionId,
			diffareaid: diffZone.diffareaid,
		};

		const fn = this._addDiffStylesToURI(uri, newDiff);
		if (typeof fn === 'function') {
			diffZone._removeStylesFns.add(fn);
		}

		this.diffOfId[diffid] = newDiff;
		diffZone._diffOfId[diffid] = newDiff;

		this._sortedDiffs[uri.fsPath].push(newDiff);
		// 当添加第一个diff时触发通知
		if (Object.keys(diffZone._diffOfId).length === 1) {
			this._onDidAddOrDeleteDiffZones.fire({ uri });
		}

		return newDiff;
	}

	// changes the start/line locations of all DiffAreas on the page (adjust their start/end based on the change) based on the change that was recently made
	private _realignAllDiffAreasLines(uri: URI, text: string, recentChange: { startLineNumber: number; endLineNumber: number }) {

		// console.log('recent change', recentChange)

		const model = this._getModel(uri);
		if (!model) return;

		// compute net number of newlines lines that were added/removed
		const startLine = recentChange.startLineNumber;
		const endLine = recentChange.endLineNumber;

		const newTextHeight = (text.match(/\n/g) || []).length + 1; // number of newlines is number of \n's + 1, e.g. "ab\ncd"

		// compute overlap with each diffArea and shrink/elongate each diffArea accordingly
		for (const diffareaid of this.diffAreasOfURI[model.uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];

			// 添加安全检查，防止数据不一致导致的错误
			if (!diffArea) {
				// 清理不一致的数据
				this.diffAreasOfURI[model.uri.fsPath].delete(diffareaid.toString());
				this._codeseekLogService.warn(`Found inconsistent diff area data: diffareaid ${diffareaid} exists in diffAreasOfURI but not in diffAreaOfId for URI ${model.uri.fsPath}`);
				continue;
			}

			// if the diffArea is entirely above the range, it is not affected
			if (diffArea.endLine < startLine) {
				// console.log('CHANGE FULLY BELOW DA (doing nothing)')
				continue;
			}
			// if a diffArea is entirely below the range, shift the diffArea up/down by the delta amount of newlines
			else if (endLine < diffArea.startLine) {
				// console.log('CHANGE FULLY ABOVE DA')
				const changedRangeHeight = endLine - startLine + 1;
				const deltaNewlines = newTextHeight - changedRangeHeight;
				diffArea.startLine += deltaNewlines;
				diffArea.endLine += deltaNewlines;
			}
			// if the diffArea fully contains the change, elongate it by the delta amount of newlines
			else if (startLine >= diffArea.startLine && endLine <= diffArea.endLine) {
				// console.log('DA FULLY CONTAINS CHANGE')
				const changedRangeHeight = endLine - startLine + 1;
				const deltaNewlines = newTextHeight - changedRangeHeight;
				diffArea.endLine += deltaNewlines;
			}
			// if the change fully contains the diffArea, make the diffArea have the same range as the change
			else if (diffArea.startLine > startLine && diffArea.endLine < endLine) {
				// console.log('CHANGE FULLY CONTAINS DA')
				diffArea.startLine = startLine;
				diffArea.endLine = startLine + newTextHeight;
			}
			// if the change contains only the diffArea's top
			else if (startLine < diffArea.startLine && diffArea.startLine <= endLine) {
				// console.log('CHANGE CONTAINS TOP OF DA ONLY')
				const numOverlappingLines = endLine - diffArea.startLine + 1;
				const numRemainingLinesInDA = diffArea.endLine - diffArea.startLine + 1 - numOverlappingLines;
				const newHeight = (numRemainingLinesInDA - 1) + (newTextHeight - 1) + 1;
				diffArea.startLine = startLine;
				diffArea.endLine = startLine + newHeight;
			}
			// if the change contains only the diffArea's bottom
			else if (startLine <= diffArea.endLine && diffArea.endLine < endLine) {
				// console.log('CHANGE CONTAINS BOTTOM OF DA ONLY')
				const numOverlappingLines = diffArea.endLine - startLine + 1;
				diffArea.endLine += newTextHeight - numOverlappingLines;
			}
		}

	}

	// 添加一个Map来存储每个URI的上次刷新时间
	private _lastRefreshTimeMap = new Map<string, number>();
	private _refreshThrottleDelay: number = 1000; // 100ms的节流时间

	// 修改原有方法实现节流
	private _refreshStylesAndDiffsInURI(uri: URI, force: boolean = false) {
		const uriKey = uri.toString();
		const now = Date.now();
		const lastTime = this._lastRefreshTimeMap.get(uriKey) || 0;

		// 如果距离上次执行不到节流时间，则直接返回
		if (now - lastTime < this._refreshThrottleDelay && !force) {
			return;
		}

		// 更新上次执行时间
		this._lastRefreshTimeMap.set(uriKey, now);

		// 执行实际的刷新操作
		// 1. clear DiffArea styles and Diffs
		this._clearAllEffects(uri);

		// 2. style DiffAreas (sweep, etc)
		this._addDiffAreaStylesToURI(uri);

		// 3. add Diffs
		this._computeDiffsAndAddStylesToURI(uri);

		// 4. refresh ctrlK zones
		this._refreshCtrlKInputs(uri);
	}

	//处理流式传输的文本（例如从语言模型 LLM 生成的代码），并将其逐步写入指定文件（通过 uri 定位），
	// 同时保持与原始代码（originalCode）的同步。它使用差异计算（diff）来确定写入位置，并动态更新文件内容
	private _writeStreamedDiffZoneLLMText(uri: URI, originalCode: string, llmTextSoFar: string, deltaText: string, latestMutable: StreamLocationMutable) {

		let numNewLines = 0;

		// ----------- 1. Write the new code to the document -----------
		// figure out where to highlight based on where the AI is in the stream right now, use the last diff to figure that out
		const computedDiffs = findDiffs(originalCode, llmTextSoFar);

		// if streaming, use diffs to figure out where to write new code
		// these are two different coordinate systems - new and old line number
		let endLineInLlmTextSoFar: number; // get file[diffArea.startLine...newFileEndLine] with line=newFileEndLine highlighted
		let startLineInOriginalCode: number; // get original[oldStartingPoint...] (line in the original code, so starts at 1)

		const lastDiff = computedDiffs.pop();

		if (!lastDiff) {
			// console.log('!lastDiff')
			// if the writing is identical so far, display no changes
			startLineInOriginalCode = 1;
			endLineInLlmTextSoFar = 1;
		}
		else {
			startLineInOriginalCode = lastDiff.originalStartLine;
			if (lastDiff.type === 'insertion' || lastDiff.type === 'edit')
				endLineInLlmTextSoFar = lastDiff.endLine;
			else if (lastDiff.type === 'deletion')
				endLineInLlmTextSoFar = lastDiff.startLine;
			else
				throw new Error(`Codeseek: diff.type not recognized on: ${lastDiff}`);
		}

		// at the start, add a newline between the stream and originalCode to make reasoning easier
		if (!latestMutable.addedSplitYet) {
			this._writeText(uri, '\n',
				{ startLineNumber: latestMutable.line, startColumn: latestMutable.col, endLineNumber: latestMutable.line, endColumn: latestMutable.col, },
				{ shouldRealignDiffAreas: true }
			);
			latestMutable.addedSplitYet = true;
			numNewLines += 1;
		}

		// insert deltaText at latest line and col
		this._writeText(uri, deltaText,
			{ startLineNumber: latestMutable.line, startColumn: latestMutable.col, endLineNumber: latestMutable.line, endColumn: latestMutable.col },
			{ shouldRealignDiffAreas: true }
		);
		const deltaNumNewLines = deltaText.split('\n').length - 1;
		latestMutable.line += deltaNumNewLines;
		const lastNewlineIdx = deltaText.lastIndexOf('\n');
		latestMutable.col = lastNewlineIdx === -1 ? latestMutable.col + deltaText.length : deltaText.length - lastNewlineIdx;
		numNewLines += deltaNumNewLines;

		// delete or insert to get original up to speed
		if (latestMutable.originalCodeStartLine < startLineInOriginalCode) {
			// moved up, delete
			const numLinesDeleted = startLineInOriginalCode - latestMutable.originalCodeStartLine;
			this._writeText(uri, '',
				{ startLineNumber: latestMutable.line, startColumn: latestMutable.col, endLineNumber: latestMutable.line + numLinesDeleted, endColumn: Number.MAX_SAFE_INTEGER, },
				{ shouldRealignDiffAreas: true }
			);
			numNewLines -= numLinesDeleted;
		}
		else if (latestMutable.originalCodeStartLine > startLineInOriginalCode) {
			const newText = '\n' + originalCode.split('\n').slice((startLineInOriginalCode - 1), (latestMutable.originalCodeStartLine - 1) - 1 + 1).join('\n');
			this._writeText(uri, newText,
				{ startLineNumber: latestMutable.line, startColumn: latestMutable.col, endLineNumber: latestMutable.line, endColumn: latestMutable.col },
				{ shouldRealignDiffAreas: true }
			);
			numNewLines += newText.split('\n').length - 1;
		}
		latestMutable.originalCodeStartLine = startLineInOriginalCode;

		return { endLineInLlmTextSoFar, numNewLines }; // numNewLines here might not be correct....
	}

	// called first, then call startApplying
	public addCtrlKZone({ startLine, endLine, editor }: AddCtrlKOpts) {

		const uri = editor.getModel()?.uri;
		if (!uri) return;

		// check if there's overlap with any other ctrlKZone and if so, focus it
		const overlappingCtrlKZone = this._findOverlappingDiffArea({ startLine, endLine, uri, filter: (diffArea) => diffArea.type === 'CtrlKZone' });
		if (overlappingCtrlKZone) {
			editor.revealLine(overlappingCtrlKZone.startLine); // important
			setTimeout(() => (overlappingCtrlKZone as CtrlKZone)._mountInfo?.textAreaRef.current?.focus(), 100);
			return;
		}

		const overlappingDiffZone = this._findOverlappingDiffArea({ startLine, endLine, uri, filter: (diffArea) => diffArea.type === 'DiffZone' });
		if (overlappingDiffZone)
			return;

		editor.revealLine(startLine);
		editor.setSelection({ startLineNumber: startLine, endLineNumber: startLine, startColumn: 1, endColumn: 1 });

		const applyId = 'ctrlK_' + generateUuid();
		const { onFinishEdit } = this._addToHistory(uri);

		const adding: Omit<CtrlKZone, 'diffareaid'> = {
			type: 'CtrlKZone',
			startLine: startLine,
			endLine: endLine,
			editorId: editor.getId(),
			applyId,
			sessionId: applyId,
			businessEvent: METRICS_EVENT.CHAT_INLINE,
			_URI: uri,
			_removeStylesFns: new Set(),
			_mountInfo: null,
			_linkedStreamingDiffZone: null,
		};
		const ctrlKZone = this._addDiffArea(adding);
		this._refreshStylesAndDiffsInURI(uri);

		onFinishEdit();
		return ctrlKZone.diffareaid;
	}

	// _remove means delete and also add to history
	public removeCtrlKZone({ diffareaid }: { diffareaid: number }) {
		const ctrlKZone = this.diffAreaOfId[diffareaid];
		if (!ctrlKZone) return;
		if (ctrlKZone.type !== 'CtrlKZone') return;

		const uri = ctrlKZone._URI;
		const { onFinishEdit } = this._addToHistory(uri);
		this._deleteCtrlKZone(ctrlKZone);
		this._refreshStylesAndDiffsInURI(uri);
		onFinishEdit();
	}

	public addTipZone({ tipArea, editor }: AddTipOpts) {
		this.tips.forEach(tip => {
			tip.dispose();
		});
		this.tips = [];
		let widget: BlankLineTipWidget | TextSelectionTipWidget | undefined = undefined;

		// 检查是否在任何 diff 区域内
		const uri = editor.getModel()?.uri;
		if (!uri) {
			return;
		}

		// 检查编辑器是否是活动的代码编辑器窗口
		const activeEditor = this._codeEditorService.getActiveCodeEditor();
		if (!activeEditor || activeEditor.getId() !== editor.getId()) {
			return;
		}

		const isInDiffArea = this._findOverlappingDiffArea({
			startLine: tipArea.type === 'BlankLineTipZone' ? tipArea.lineNumber : tipArea.startLine,
			endLine: tipArea.type === 'BlankLineTipZone' ? tipArea.lineNumber : tipArea.endLine,
			uri,
			filter: (diffArea) => diffArea.type === 'DiffZone'
		});
		// 如果在 diff 区域内，不显示提示框
		if (isInDiffArea) {
			return;
		}

		if (tipArea.type === tipTypes[0]) {
			widget = new BlankLineTipWidget(
				{
					editor,
					lineNumber: tipArea.lineNumber,
					column: tipArea.column,
					showPosition: tipArea.showPosition,
				},
				this
			);
		} else if (tipArea.type === tipTypes[1]) {
			const isOpenChat = this._sidebarStateService.isSidebarChatOpen();
			const isOpenEdit = this._quickEditStateService.isOpenEdit();
			const props = {
				editor,
				startLine: tipArea.startLine,
				endLine: tipArea.endLine,
				isOpenChat,
				isOpenEdit,
				onOpenChat: async () => {
					this.disposeTip('TextSelectionTipZone');
					await this._commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);
					await this._commandService.executeCommand(CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID);
				},
				onOpenEdit: () => {
					this.disposeTip('TextSelectionTipZone');
					this._commandService.executeCommand(CODESEEK_CTRL_K_ACTION_ID);
					this._quickEditStateService.setState({ isOpenEdit: true });
				},
				onExplainCode: () => {
					this.disposeTip('TextSelectionTipZone');
					this._commandService.executeCommand(CODESEEK_EXPLAIN_CODE_ACTION_ID);
				},
				onRefactorCode: () => {
					this.disposeTip('TextSelectionTipZone');
					this._commandService.executeCommand(CODESEEK_REFACTOR_CODE_ACTION_ID);
				},
			};

			widget = new TextSelectionTipWidget(props, this, this._sidebarStateService, this._quickEditStateService);
		}
		if (widget) {
			editor.addOverlayWidget(widget);
			this.tips.push(widget);
		}
	}

	public disposeTip(type?: (typeof tipTypes)[number]): void {
		if (type) {
			const tipsToDispose: (BlankLineTipWidget | TextSelectionTipWidget)[] = [];
			const tipsToKeep: (BlankLineTipWidget | TextSelectionTipWidget)[] = [];

			this.tips.forEach(tip => {
				if (tip.getType() === type) {
					tipsToDispose.push(tip);
				} else {
					tipsToKeep.push(tip);
				}
			});
			tipsToDispose.forEach(tip => tip.dispose());
			this.tips = tipsToKeep;
		} else {
			this.tips.forEach(tip => {
				tip.dispose();
			});
			this.tips = [];
		}
	}


	public async startApplying(opts: StartApplyingOpts) {
		const applyId = 'apply-' + generateUuid();
		if (opts.from === 'ClickApply' && opts.applyBoxId) {
			this.applyingURIOfApplyBoxIdRef.current[opts.applyBoxId] = opts.uri ?? undefined
		}

		// 在Apply操作开始时发送事件通知
		if (opts.from === 'ClickApply' && opts.uri) {
			this._onDidStartApplying.fire({ uri: opts.uri, applyId: opts.applyBoxId || 'unknown' });
		}

		if (opts.type === 'rewrite' && opts.from === 'ClickApply') {
			const addedDiffArea = await this._rewriteCodeStream(opts, applyId);
			return addedDiffArea?._URI ?? null;
		} else if (opts.type === 'rewrite') {
			const addedDiffArea = await this._initializeWriteoverStream(opts, opts.chatId);
			return addedDiffArea?._URI ?? null;
		}
		else if (opts.type === 'searchReplace') {
			const addedDiffArea = await this._initializeSearchAndReplaceStream(opts, applyId);
			return addedDiffArea?._URI ?? null;
		}
		else if (opts.type === 'explain' || opts.type === 'refactor') {
			let diffZone: DiffZone | undefined;
			const abortController = new AbortController();
			const timeoutId = setTimeout(() => {
				this._codeseekLogService.error(`the ${opts.type} command execute timeout, the timeout time is 10 seconds`);
				abortController.abort();
				if (diffZone) {
					this._revertAndDeleteDiffZone(diffZone);
				}
			}, 60000);

			try {
				diffZone = await this._initializeWriteoverStream(opts, opts.chatId, abortController.signal);
			} catch (error) {
				this._codeseekLogService.error(`Error executing ${opts.type} command:`, error);
			} finally {
				clearTimeout(timeoutId);
			}
		}

		return null;
	}

	private async _rewriteCodeStream(opts: StartApplyingOpts, applyId: string, abortSignal?: AbortSignal): Promise<DiffZone | undefined> {
		this._codeseekLogService.info('rewriteCodeStream', JSON.stringify(opts));
		const { from, chatId, sessionId, businessEvent, applyStr } = opts as {
			from: 'ClickApply';
			type: 'searchReplace' | 'rewrite';
			applyStr: string;
			uri: URI;
			applyBoxId?: string;
			chatId: string;
			sessionId: string;
			businessEvent: string;
		};
		let startLine: number;
		let endLine: number;
		let uri: URI;


		// 优先使用调用方传入的 URI，避免依赖当前活动编辑器
		if ('uri' in opts && opts.uri) {
			uri = opts.uri as URI;
		} else {
			const uri_ = this._getActiveEditorURI();
			if (!uri_) return;
			uri = uri_;
		}

		// 打开文件但不激活当前编辑器（editCodeService内部默认 preserveFocus:true）
		await this.openOrCreateFile(uri, true, false);
		// 创建modelRef
		const modelRef = await this._textModelService.createModelReference(uri);

		// 等待编辑器准备好，检查模型是否已加载
		const waitForEditor = async () => {
			const maxAttempts = 50; // 最大等待5秒
			for (let i = 0; i < maxAttempts; i++) {
				const model = this._modelService.getModel(uri);
				if (model && !model.isDisposed()) {
					return true;
				}
				await new Promise(resolve => setTimeout(resolve, 100));
			}
			return false;
		};

		const isReady = await waitForEditor();
		if (!isReady) {
			console.warn('Editor not ready after waiting');
			// 在失败时清理Apply状态
			modelRef.dispose();
			return;
		}


		// 获取当前文件内容（用于LLM处理）
		const currentFileStr = this._readURI(uri);
		if (currentFileStr === null) {
			modelRef.dispose();
			return;
		}

		if (!this.originalFileContentBeforeAnyApply.has(uri.fsPath)) {
			// 只在第一次apply时保存原始内容
			// 后续的基准更新在acceptDiff时进行
			this.originalFileContentBeforeAnyApply.set(uri.fsPath, currentFileStr);
		}

		// accept all diffZones on this URI, keeping the current file state (保持文件当前状态，只清除diff显示)
		this._deleteAllDiffAreas(uri);
		this.diffAreasOfURI[uri.fsPath].clear();

		// in ctrl+L the start and end lines are the full document
		const numLines = this._getNumLines(uri);
		if (numLines === null) {
			modelRef.dispose();
			return;
		}
		startLine = 1;
		endLine = numLines;
		if (numLines >= 1500) {
			const selectedWindow = await this._largeFileApplyService.analyzeAndSelect({
				targetFileContent: currentFileStr,
				smallFileContent: applyStr,
			});
			startLine = selectedWindow.startLine;
			endLine = selectedWindow.endLine;
		}

		// apply的内容用最新的，diff对比用老的
		// LLM处理使用当前最新文件内容
		const currentCodeForLLM = currentFileStr.split('\n').slice((startLine - 1), (endLine - 1) + 1).join('\n');

		// diff对比使用保存的原始文件内容，显示累积变更
		const originalFileContent = this.originalFileContentBeforeAnyApply.has(uri.fsPath) ? this.originalFileContentBeforeAnyApply.get(uri.fsPath) as string : currentFileStr;
		const originalCodeForDiff = originalFileContent.split('\n').slice((startLine - 1), (endLine - 1) + 1).join('\n');
		const streamRequestIdRef: { current: string | null } = { current: null };
		// add to history
		const { onFinishEdit } = this._addToHistory(uri);

		const adding: Omit<DiffZone, 'diffareaid'> = {
			type: 'DiffZone',
			originalCode: originalCodeForDiff, // diff对比使用原始内容
			startLine,
			endLine,
			_URI: uri,
			applyId,
			businessEvent,
			sessionId,
			_streamState: {
				isStreaming: true,
				streamRequestIdRef,
				line: startLine,
				applyBoxId: (opts as any).applyBoxId,
			},
			_diffOfId: {}, // added later
			_removeStylesFns: new Set(),
		};
		const diffZone = this._addDiffArea(adding);
		this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid });
		this._onDidAddOrDeleteDiffZones.fire({ uri });

		// 立即触发URI流状态更新事件，确保UI能及时响应状态变化
		const currentState = this.getURIStreamState({ uri, applyBoxId: (opts as any).applyBoxId });
		this._onDidChangeURIStreamState.fire({ uri, state: currentState });

		// now handle messages
		let messages: LLMChatMessage[];
		// LLM处理使用当前最新文件内容
		const userContent = rewriteCodeUserMessage(currentCodeForLLM, applyStr);

		messages = [
			{ role: 'system', content: rewriteCodeSystemMessage, },
			{ role: 'user', content: userContent, }
		];


		const onDone = (originCode?: string) => {

			diffZone._streamState = { isStreaming: false, applyBoxId: diffZone._streamState.applyBoxId };

			// 🟢 先刷新样式和计算差异，确保 _sortedDiffs 被正确填充
			this._refreshStylesAndDiffsInURI(uri, true);

			// 🟢 然后再触发事件，这样 AcceptAllRejectAllWidget 就能正确显示
			if (originCode) {
				this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid, originCode });
			} else {
				this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid });
			}

			// 在Apply操作结束时发送事件通知
			if (from === 'ClickApply') {
				this._onDidEndApplying.fire({ uri, applyId: diffZone._streamState.applyBoxId || 'unknown' });
			}

			onFinishEdit();
		};

		if (currentFileStr.trim() === '') {
			this._writeText(uri, applyStr,
				{ startLineNumber: diffZone.startLine, startColumn: 1, endLineNumber: diffZone.endLine, endColumn: Number.MAX_SAFE_INTEGER }, // 1-indexed
				{ shouldRealignDiffAreas: true }
			);
			onDone(originalFileContent);
			return diffZone;
		}

		// refresh now in case onText takes a while to get 1st message
		this._refreshStylesAndDiffsInURI(uri, true);


		const latestStreamInfoMutable: StreamLocationMutable = { line: diffZone.startLine, addedSplitYet: false, col: 1, originalCodeStartLine: 1 };

		// state used in onText:
		let fullText = '';
		const startTime = Date.now();
		let firstTokenTime: number | null = null;
		const applyModel = this._codeseekSettingsService.getModelSelectionForContainer(FeatureNames.Apply);
		const modelSelection = applyModel ? applyModel : {
			providerName: ProviderNames.default,
			modelName: 'AI-IDE-FastApply',
			isApplyModel: true,
		};

		const useProviderFor = FeatureNames.Apply;
		const reporter = (requestId: string | null, response: string, totalCostTime: number, error?: any) => {
			const firstTokenCostTime = firstTokenTime ? (firstTokenTime - startTime) : 0;

			const traceIdPair = { traceId: applyId, parentTraceId: chatId, sessionId };
			const metricsData = {
				eventContent: {
					systemPrompt: rewriteCodeSystemMessage,
					userInstruct: userContent,
					modelName: modelSelection.modelName,
					request: [{
						requestId: requestId ?? '',
						tool: '',
						input: JSON.stringify(messages),
						response,
						applyFile: uri.fsPath,
						originalCode: applyStr,
						modelId: modelSelection.modelName,
						firstTokenCostTime,
						startTime: new Date(startTime + 8 * 60 * 60 * 1000).toISOString().replace('T', ' ').replace('Z', '').substring(0, 23),
						costtime: totalCostTime,
						result: error ? 'failed' : 'success',
						error
					}],
					totaltime: totalCostTime,
				},
				traceIdPair
			}
			this._metricsService.capture(METRICS_EVENT.APPLY, metricsData);

		};
		if (abortSignal?.aborted) {
			this._revertAndDeleteDiffZone(diffZone);
			modelRef.dispose();
			return;
		}
		const user = await this._codeseekUacLoginService.getUserInfo();
		this._codeseekLogService.info('[editCodeService] sendLLMMessage', JSON.stringify(messages));
		streamRequestIdRef.current = this._llmMessageService.sendLLMMessage({
			messagesType: 'applyMessages',
			useProviderFor,
			modelSelection: modelSelection,
			logging: { loggingName: `startApplying - ${from}` },
			messages,
			userId: user?.userId ?? '',
			isThrottle: false,
			onText: ({ newText: newText_ }) => {
				if (abortSignal?.aborted) {
					this._revertAndDeleteDiffZone(diffZone);
					this._llmMessageService.abort(streamRequestIdRef.current ?? '');
					return;
				}
				firstTokenTime = firstTokenTime ?? Date.now();

				fullText += newText_;

				const { endLineInLlmTextSoFar } = this._writeStreamedDiffZoneLLMText(uri, currentCodeForLLM, fullText, newText_, latestStreamInfoMutable);
				diffZone._streamState.line = (diffZone.startLine - 1) + endLineInLlmTextSoFar; // change coordinate systems from originalCode to full file

				this._refreshStylesAndDiffsInURI(uri);

			},
			onFinalMessage: async ({ fullText }) => {
				const totalCostTime = Date.now() - startTime;
				if (abortSignal?.aborted) {
					this._revertAndDeleteDiffZone(diffZone);
					this._llmMessageService.abort(streamRequestIdRef.current ?? '');
					return;
				}
				reporter(streamRequestIdRef.current, fullText, totalCostTime);
				this._writeText(uri, fullText,
					{ startLineNumber: diffZone.startLine, startColumn: 1, endLineNumber: diffZone.endLine, endColumn: Number.MAX_SAFE_INTEGER }, // 1-indexed
					{ shouldRealignDiffAreas: true }
				);
				onDone(originalFileContent);
				modelRef.dispose();
			},
			onError: (e) => {
				const totalCostTime = Date.now() - startTime;
				this._revertAndDeleteDiffZone(diffZone);
				this._notifyError(e);
				onDone();
				this._undoHistory(uri);
				reporter(streamRequestIdRef.current, '', totalCostTime, e.message);
				modelRef.dispose();
			},

		});
		return diffZone;

	}

	private _findOverlappingDiffArea({ startLine, endLine, uri, filter }: { startLine: number; endLine: number; uri: URI; filter?: (diffArea: DiffArea) => boolean }): DiffArea | null {
		// check if there's overlap with any other diffAreas and return early if there is
		for (const diffareaid of this.diffAreasOfURI[uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (!diffArea) continue;
			if (!filter?.(diffArea)) continue;
			const noOverlap = diffArea.startLine > endLine || diffArea.endLine < startLine;
			if (!noOverlap) {
				return diffArea;
			}
		}
		return null;
	}

	private async _initializeWriteoverStream(opts: StartApplyingOpts, applyId: string, abortSignal?: AbortSignal): Promise<DiffZone | undefined> {
		this._codeseekLogService.info('initializeWriteoverStream', JSON.stringify(opts));
		const { from, chatId, sessionId, businessEvent } = opts;
		let startLine: number;
		let endLine: number;
		let uri: URI;
		let instructions: string;
		let selections: StagingSelectionItem[] = []

		if (from === 'ClickApply') {

			const uri_ = this._getActiveEditorURI();
			if (!uri_) return;
			uri = uri_;

			// reject all diffZones on this URI, adding to history (there can't possibly be overlap after this)
			this._deleteAllDiffAreas(uri);
			this.diffAreasOfURI[uri.fsPath].clear();

			// in ctrl+L the start and end lines are the full document
			const numLines = this._getNumLines(uri);
			if (numLines === null) return;
			startLine = 1;
			endLine = numLines;

		}
		else if (from === 'QuickEdit') {
			if (opts.type === 'rewrite') {
				const { diffareaid } = opts;
				const ctrlKZone = this.diffAreaOfId[diffareaid];

				// 添加安全检查
				if (!ctrlKZone) {
					this._codeseekLogService.warn(`CtrlKZone not found for diffareaid ${diffareaid} in _initializeWriteoverStream`);
					return;
				}

				if (ctrlKZone.type !== 'CtrlKZone') return;

				const { startLine: startLine_, endLine: endLine_, _URI, _mountInfo } = ctrlKZone;
				uri = _URI;
				startLine = startLine_;
				endLine = endLine_;
				selections = _mountInfo?.selections.current || []
				instructions = _mountInfo?.textAreaRef.current?.value ?? '';
			}
			else if (opts.type === 'explain' || opts.type === 'refactor') {
				instructions = opts.instructions;
				uri = opts.uri;
				startLine = opts.startLine;
				endLine = opts.endLine;
			}
			else {
				throw new Error(`Codeseek: diff.type not recognized on: ${from}`);
			}
		}
		else {
			throw new Error(`Codeseek: diff.type not recognized on: ${from}`);
		}

		const currentFileStr = this._readURI(uri);
		if (currentFileStr === null) return;
		const originalCode = currentFileStr.split('\n').slice((startLine - 1), (endLine - 1) + 1).join('\n');
		const streamRequestIdRef: { current: string | null } = { current: null };
		// add to history
		const { onFinishEdit } = this._addToHistory(uri);
		// __TODO__ let users customize modelFimTags
		const quickEditFIMTags = defaultQuickEditFimTags;

		const adding: Omit<DiffZone, 'diffareaid'> = {
			type: 'DiffZone',
			originalCode,
			startLine,
			endLine,
			_URI: uri,
			applyId,
			businessEvent,
			sessionId,
			_streamState: {
				isStreaming: true,
				streamRequestIdRef,
				line: startLine,
			},
			_diffOfId: {}, // added later
			_removeStylesFns: new Set(),
		};
		const diffZone = this._addDiffArea(adding);
		this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid });
		this._onDidAddOrDeleteDiffZones.fire({ uri });

		if (from === 'QuickEdit') {
			if (opts.type === 'rewrite') {
				const { diffareaid } = opts;
				const ctrlKZone = this.diffAreaOfId[diffareaid];
				if (ctrlKZone.type !== 'CtrlKZone') return;

				ctrlKZone._linkedStreamingDiffZone = diffZone.diffareaid;
				this._onDidChangeCtrlKZoneStreaming.fire({ uri, diffareaid: ctrlKZone.diffareaid });
			}
		}

		// now handle messages
		let messages: LLMChatMessage[];
		let systemPrompt: string = rewriteCode_systemMessage;
		let userContent: string = '';
		if (from === 'ClickApply') {
			userContent = rewriteCode_userMessage({ originalCode, applyStr: opts.applyStr, uri });
			messages = [
				{ role: 'system', content: rewriteCode_systemMessage, },
				{ role: 'user', content: userContent, }
			];
		}
		else if (from === 'QuickEdit') {
			const { prefix, suffix } = codeseekPrefixAndSuffix({ fullFileStr: currentFileStr, startLine, endLine });
			const language = filenameToVscodeLanguage(uri.fsPath) ?? '';
			userContent = await ctrlKStream_userMessage({
				selection: originalCode,
				selections,
				instructions: instructions!,
				prefix,
				suffix,
				isOllamaFIM: false,
				fimTags: quickEditFIMTags,
				language,
				codeseekExporerService: this._codeseekExporerService,
				codeseekFileService: this._codeseekFileService,
				modelService: this._modelService,
				workspaceContextService: this._workspaceService
			});
			// type: 'messages',
			systemPrompt = ctrlKStream_systemMessage({ quickEditFIMTags: quickEditFIMTags, rules: this._codeseekSettingsService.state.globalSettings.userRules })
			messages = [
				{ role: 'system', content: systemPrompt },
				{ role: 'user', content: userContent, }
			];
		}
		else { throw new Error(`featureName ${from} is invalid`); }


		const onDone = (originCode?: string) => {

			diffZone._streamState = { isStreaming: false, applyBoxId: diffZone._streamState.applyBoxId };
			if (originCode) {
				this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid, originCode });
			} else {
				this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid });
			}

			if (from === 'QuickEdit') {
				if (opts.type === 'rewrite') {
					const ctrlKZone = this.diffAreaOfId[opts.diffareaid] as CtrlKZone;

					// 添加安全检查
					if (!ctrlKZone) {
						this._codeseekLogService.warn(`CtrlKZone not found for diffareaid ${opts.diffareaid} in onDone callback`);
						return;
					}

					ctrlKZone._linkedStreamingDiffZone = null;
					this._onDidChangeCtrlKZoneStreaming.fire({ uri, diffareaid: ctrlKZone.diffareaid });
					this._deleteCtrlKZone(ctrlKZone);
				}
			}

			// 在Apply操作结束时发送事件通知
			if (from === 'ClickApply') {
				this._onDidEndApplying.fire({ uri, applyId: diffZone._streamState.applyBoxId || 'unknown' });
			}

			this._refreshStylesAndDiffsInURI(uri, true);
			onFinishEdit();
		};

		// refresh now in case onText takes a while to get 1st message
		this._refreshStylesAndDiffsInURI(uri, true);



		const extractText = (fullText: string, recentlyAddedTextLen: number) => {
			if (from === 'QuickEdit') {
				return extractCodeFromFIM({ text: fullText, recentlyAddedTextLen, midTag: quickEditFIMTags.midTag });
			}
			else if (from === 'ClickApply') {
				return extractCodeFromRegular({ text: fullText, recentlyAddedTextLen });
			}
			throw 1;
		};

		const latestStreamInfoMutable: StreamLocationMutable = { line: diffZone.startLine, addedSplitYet: false, col: 1, originalCodeStartLine: 1 };

		const useProviderFor = opts.from === 'ClickApply' ? FeatureNames.Apply : FeatureNames.CtrlK
		// state used in onText:
		let fullText = '';
		let prevIgnoredSuffix = '';
		const startTime = Date.now();
		let firstTokenTime: number | null = null;
		let modelSelection: ModelSelection | undefined;
		if (opts.type === 'explain' || opts.type === 'refactor') {
			modelSelection = {
				providerName: ProviderNames.default,
				modelName: 'AI-IDE-Chat',
				isApplyModel: true,
			}
		} else {
			modelSelection = this._codeseekSettingsService.getModelSelectionForContainer(useProviderFor) ?? undefined;
		}
		const reporter = (requestId: string | null, costtime: number, error?: string) => {
			const firstTokenCostTime = firstTokenTime ? (firstTokenTime - startTime) : 0;
			const response = fullText;
			const modelName = modelSelection?.modelName || ''
			if (opts.from === 'QuickEdit') {
				const traceIdPair = { traceId: chatId, parentTraceId: '', sessionId };
				const metricsData = {
					eventContent: {
						systemPrompt: systemPrompt,
						userInstruct: instructions,
						modelName,
						request: [{
							requestId: requestId || '',
							input: JSON.stringify(messages),
							response,
							modelId: modelName,
							result: error ? 'failed' : 'success',
							firstTokenCostTime,
							costtime,
							error
						}]
					},
					traceIdPair
				}
				this._metricsService.capture(METRICS_EVENT.CHAT_INLINE, metricsData);
			} else if (opts.from === 'ClickApply') {
				const traceIdPair = { traceId: applyId, parentTraceId: chatId, sessionId };
				const metricsData = {
					eventContent: {
						systemprompt: systemPrompt,
						useinstruct: userContent,
						modelName,
						request: [{
							request_id: requestId || '',
							input: JSON.stringify(messages),
							response,
							applyFile: uri.fsPath,
							originalCode: opts.applyStr,
							model_id: modelName,
							result: error ? 'failed' : 'success',
							firstTokenCostTime,
							costtime,
							error
						}]
					},
					traceIdPair
				}
				this._metricsService.capture(METRICS_EVENT.APPLY, metricsData);
			}
		};
		if (abortSignal?.aborted) {
			this._revertAndDeleteDiffZone(diffZone);
			return;
		}
		const user = await this._codeseekUacLoginService.getUserInfo();
		this._codeseekLogService.info('[editCodeService] sendLLMMessage', JSON.stringify(messages));
		streamRequestIdRef.current = this._llmMessageService.sendLLMMessage({
			messagesType: 'chatMessages',
			useProviderFor,
			modelSelection: modelSelection,
			logging: { loggingName: `startApplying - ${from}` },
			messages,
			userId: user?.userId ?? '',
			isThrottle: false,
			onText: ({ newText: newText_ }) => {
				if (abortSignal?.aborted) {
					this._revertAndDeleteDiffZone(diffZone);
					this._llmMessageService.abort(streamRequestIdRef.current ?? '');
					return;
				}
				firstTokenTime = firstTokenTime ?? Date.now();

				const newText = prevIgnoredSuffix + newText_; // add the previously ignored suffix because it's no longer the suffix!
				fullText += prevIgnoredSuffix + newText; // full text, including ```, etc

				const [croppedText, deltaCroppedText, croppedSuffix] = extractText(fullText, newText.length);
				const { endLineInLlmTextSoFar } = this._writeStreamedDiffZoneLLMText(uri, originalCode, croppedText, deltaCroppedText, latestStreamInfoMutable);
				diffZone._streamState.line = (diffZone.startLine - 1) + endLineInLlmTextSoFar; // change coordinate systems from originalCode to full file

				this._refreshStylesAndDiffsInURI(uri);

				prevIgnoredSuffix = croppedSuffix;
			},
			onFinalMessage: async ({ fullText }) => {
				const totaltime = Date.now() - startTime;
				if (abortSignal?.aborted) {
					this._revertAndDeleteDiffZone(diffZone);
					this._llmMessageService.abort(streamRequestIdRef.current ?? '');
					return;
				}
				reporter(streamRequestIdRef.current, totaltime);
				const [croppedText, _1, _2] = extractText(fullText, 0);
				this._writeText(uri, croppedText,
					{ startLineNumber: diffZone.startLine, startColumn: 1, endLineNumber: diffZone.endLine, endColumn: Number.MAX_SAFE_INTEGER }, // 1-indexed
					{ shouldRealignDiffAreas: true }
				);
				const originalFileContent = this.originalFileContentBeforeAnyApply.has(uri.fsPath) ? this.originalFileContentBeforeAnyApply.get(uri.fsPath) as string : currentFileStr;
				onDone(originalFileContent);
			},
			onError: (e) => {
				const totaltime = Date.now() - startTime;
				this._revertAndDeleteDiffZone(diffZone);
				this._notifyError(e);
				onDone();
				this._undoHistory(uri);
				reporter(streamRequestIdRef.current, totaltime, e.message);
			},

		});
		return diffZone;

	}

	private async _initializeSearchAndReplaceStream(opts: StartApplyingOpts & { from: 'ClickApply' }, applyId: string) {
		const { applyStr, uri, applyBoxId, chatId, sessionId } = opts;

		if (!uri) return;

		// generate search/replace block text
		const originalFileCode = this._codeseekFileService.readModel(uri);
		if (originalFileCode === null) return;

		const numLines = this._getNumLines(uri);
		if (numLines === null) return;

		// reject all diffZones on this URI, adding to history (there can't possibly be overlap after this)
		this._deleteAllDiffAreas(uri);
		this.diffAreasOfURI[uri.fsPath].clear();

		const startLine = 1;
		const endLine = numLines;

		const userMessageContent = searchReplace_userMessage({ originalCode: originalFileCode, applyStr: applyStr });
		const messages: LLMChatMessage[] = [
			{ role: 'system', content: searchReplace_systemMessage },
			{ role: 'user', content: userMessageContent },
		];

		// can use this as a proxy to set the diffArea's stream state requestId
		const streamRequestIdRef: { current: string | null } = { current: null };

		let { onFinishEdit } = this._addToHistory(uri);

		// TODO replace these with whatever block we're on initially if already started

		type SearchReplaceDiffAreaMetadata = {
			originalBounds: [number, number]; // 1-indexed
			originalCode: string;
		};

		const addedTrackingZoneOfBlockNum: TrackingZone<SearchReplaceDiffAreaMetadata>[] = [];

		const adding: Omit<DiffZone, 'diffareaid'> = {
			type: 'DiffZone',
			originalCode: originalFileCode,
			startLine,
			endLine,
			applyId,
			sessionId,
			businessEvent: opts.businessEvent,
			_URI: uri,
			_streamState: {
				isStreaming: true,
				streamRequestIdRef,
				line: startLine,
				applyBoxId,
			},
			_diffOfId: {}, // added later
			_removeStylesFns: new Set(),
		};
		const diffZone = this._addDiffArea(adding);
		this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid });
		this._onDidAddOrDeleteDiffZones.fire({ uri });


		const revertAndContinueHistory = () => {
			this._undoHistory(uri);
			const { onFinishEdit: onFinishEdit_ } = this._addToHistory(uri);
			onFinishEdit = onFinishEdit_;
		};


		// 将原始代码的行范围映射到最终代码的行范围，考虑之前替换的行数变化
		const convertOriginalRangeToFinalRange = (originalRange: readonly [number, number]): [number, number] => {
			// 通过计算行偏移量来调整范围
			const [originalStart, originalEnd] = originalRange;
			let lineOffset = 0;

			// 遍历所有已追踪的代码块
			for (const blockDiffArea of addedTrackingZoneOfBlockNum) {
				const {
					startLine, endLine, // 当前代码块的起止行
					metadata: { originalBounds: [originalStart2, originalEnd2], }, // 原始代码的起止行
				} = blockDiffArea;

				// 如果原始代码块的开始行在当前范围之后,跳过这个块
				if (originalStart2 >= originalEnd) continue;

				// 计算新代码和原始代码的行数差异
				const numNewLines = endLine - startLine + 1; // 新代码的行数
				const numOldLines = originalEnd2 - originalStart2 + 1; // 原始代码的行数

				// 累加行偏移量(可能为正也可能为负)
				lineOffset += numNewLines - numOldLines;
			}

			// 返回调整后的范围[起始行,结束行]
			return [originalStart + lineOffset, originalEnd + lineOffset];
		};


		const errMsgOfInvalidStr = (str: string & ReturnType<typeof findTextInCode>) => {
			return str === 'Not found' ?
				'I interrupted you because the latest ORIGINAL code could not be found in the file. Please output all SEARCH/REPLACE blocks again, making sure the code in ORIGINAL is identical to a code snippet in the file.'
				: str === 'Not unique' ?
					'I interrupted you because the latest ORIGINAL code shows up multiple times in the file. Please output all SEARCH/REPLACE blocks again, making sure the code in each ORIGINAL section is unique in the file.'
					: '';
		};


		const onDone = (change: boolean = true) => {
			diffZone._streamState = { isStreaming: false, applyBoxId: diffZone._streamState.applyBoxId };
			if (change) {
				this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid });
			} else {
				this._deleteAllDiffAreas(uri);
			}

			// 在Apply操作结束时发送事件通知
			this._onDidEndApplying.fire({ uri, applyId });

			this._refreshStylesAndDiffsInURI(uri, true);


			// delete the tracking zones
			for (const trackingZone of addedTrackingZoneOfBlockNum)
				this._deleteTrackingZone(trackingZone);

			onFinishEdit();
		};

		// refresh now in case onText takes a while to get 1st message
		this._refreshStylesAndDiffsInURI(uri, true);

		// stream style related
		let latestStreamLocationMutable: StreamLocationMutable | null = null;
		let shouldUpdateOrigStreamStyle = true;

		let oldBlocks: ExtractedSearchReplaceBlock[] = [];

		// this generates >>>>>>> ORIGINAL <<<<<<< REPLACE blocks and and simultaneously applies it
		let shouldSendAnotherMessage = true;
		let nMessagesSent = 0;
		let currStreamingBlockNum = 0;
		let firstTokenTime: number | null = null;
		const startTime = Date.now();
		const modelName = this._codeseekSettingsService.getModelSelectionForContainer(FeatureNames.Apply)?.modelName ?? '';
		const reporter = (response: string, requestId: string | null, error?: any) => {
			const firstTokenCostTime = firstTokenTime ? (firstTokenTime - startTime) : 0;
			const totalCostTime = Date.now() - startTime;
			const traceIdPair = { traceId: applyId, parentTraceId: chatId, sessionId };
			const metricsData = {
				eventContent: {
					systemprompt: searchReplace_systemMessage,
					useinstruct: userMessageContent,
					modelName,
					request: [{
						requestId: requestId || '',
						input: JSON.stringify(messages),
						response,
						applyFile: uri.fsPath,
						originalCode: applyStr,
						model_id: modelName,
						result: error ? 'failed' : 'success',
						firstTokenCostTime,
						costtime: totalCostTime,
						error
					}]
				},
				traceIdPair
			}
			this._metricsService.capture(METRICS_EVENT.APPLY, metricsData);
		};
		const user = await this._codeseekUacLoginService.getUserInfo();
		while (shouldSendAnotherMessage) {
			shouldSendAnotherMessage = false;
			nMessagesSent += 1;

			streamRequestIdRef.current = this._llmMessageService.sendLLMMessage({
				messagesType: 'chatMessages',
				useProviderFor: FeatureNames.Apply,
				logging: { loggingName: `generateSearchAndReplace` },
				messages,
				userId: user?.userId ?? '',
				onText: ({ fullText }) => {
					firstTokenTime = firstTokenTime ?? Date.now();
					// blocks are [done done done ... {writingFinal|writingOriginal}]
					//               ^
					//              currStreamingBlockNum
					// const blocks = extractSearchReplaceBlocks(fullText);

					// for (let blockNum = currStreamingBlockNum; blockNum < blocks.length; blockNum += 1) {
					// 	const block = blocks[blockNum];

					// 	if (block.state === 'writingOriginal') {
					// 		// update stream state to the first line of original if some portion of original has been written
					// 		if (shouldUpdateOrigStreamStyle && block.orig.trim().length >= 20) {
					// 			const startingAtLine = diffZone._streamState.line ?? 1; // dont go backwards if already have a stream line
					// 			const originalRange = findTextInCode(block.orig, originalFileCode, startingAtLine);
					// 			if (typeof originalRange !== 'string') {
					// 				const [startLine, _] = convertOriginalRangeToFinalRange(originalRange);
					// 				diffZone._streamState.line = startLine;
					// 				shouldUpdateOrigStreamStyle = false;
					// 			}
					// 		}
					// 		// must be done writing original to move on to writing streamed content
					// 		continue;
					// 	}
					// 	shouldUpdateOrigStreamStyle = true;


					// 	// if this is the first time we're seeing this block, add it as a diffarea so we can start streaming
					// 	if (!(blockNum in addedTrackingZoneOfBlockNum)) {
					// 		const originalBounds = findTextInCode(block.orig, originalFileCode);

					// 		// if error
					// 		if (typeof originalBounds === 'string') {
					// 			messages.push(
					// 				{ role: 'assistant', content: fullText }, // latest output
					// 				{ role: 'user', content: errMsgOfInvalidStr(originalBounds) } // user explanation of what's wrong
					// 			);
					// 			if (streamRequestIdRef.current) this._llmMessageService.abort(streamRequestIdRef.current);
					// 			shouldSendAnotherMessage = true;
					// 			revertAndContinueHistory();
					// 			continue;
					// 		}

					// 		const [startLine, endLine] = convertOriginalRangeToFinalRange(originalBounds);

					// 		// otherwise if no error, add the position as a diffarea
					// 		const adding: Omit<TrackingZone<SearchReplaceDiffAreaMetadata>, 'diffareaid'> = {
					// 			type: 'TrackingZone',
					// 			startLine: startLine,
					// 			endLine: endLine,
					// 			_URI: uri,
					// 			metadata: {
					// 				originalBounds: [...originalBounds],
					// 				originalCode: block.orig,
					// 			},
					// 		};
					// 		const trackingZone = this._addDiffArea(adding);
					// 		addedTrackingZoneOfBlockNum.push(trackingZone);
					// 		latestStreamLocationMutable = { line: startLine, addedSplitYet: false, col: 1, originalCodeStartLine: 1 };
					// 	} // <-- done adding diffarea


					// 	// should always be in streaming state here
					// 	if (!diffZone._streamState.isStreaming) {
					// 		console.error('DiffZone was not in streaming state in _initializeSearchAndReplaceStream');
					// 		continue;
					// 	}
					// 	if (!latestStreamLocationMutable) continue;

					// 	// if a block is done, finish it by writing all
					// 	if (block.state === 'done') {
					// 		const { startLine: finalStartLine, endLine: finalEndLine } = addedTrackingZoneOfBlockNum[blockNum];
					// 		this._writeText(uri, block.final,
					// 			{ startLineNumber: finalStartLine, startColumn: 1, endLineNumber: finalEndLine, endColumn: Number.MAX_SAFE_INTEGER }, // 1-indexed
					// 			{ shouldRealignDiffAreas: true }
					// 		);
					// 		diffZone._streamState.line = finalEndLine + 1;
					// 		currStreamingBlockNum = blockNum + 1;
					// 		continue;
					// 	}

					// 	// write the added text to the file
					// 	const deltaFinalText = block.final.substring((oldBlocks[blockNum]?.final ?? '').length, Infinity);
					// 	this._writeStreamedDiffZoneLLMText(uri, block.orig, block.final, deltaFinalText, latestStreamLocationMutable);
					// 	oldBlocks = blocks; // oldblocks is only used if writingFinal

					// 	// const { endLine: currentEndLine } = addedTrackingZoneOfBlockNum[blockNum] // would be bad to do this because a lot of the bottom lines might be the same. more accurate to go with latestStreamLocationMutable
					// 	// diffZone._streamState.line = currentEndLine
					// 	diffZone._streamState.line = latestStreamLocationMutable.line;
					// } // end for

					// this._refreshStylesAndDiffsInURI(uri);
				},
				onFinalMessage: async ({ fullText }) => {
					reporter(fullText, streamRequestIdRef.current);
					this._codeseekLogService.info('apply final message', fullText);
					if (originalFileCode && originalFileCode.includes('<<<<<<< HEAD')) {
						this._notificationService.info(`${uri.fsPath} 一键Apply失败，请手工合入，功能拼命优化中 ···`);
						onDone(false);
						return;
					}

					// 1. wait 500ms and fix lint errors - call lint error workflow
					// (update react state to say "Fixing errors")

					let blocks = extractSearchReplaceBlocks(fullText);
					if (originalFileCode.trim() === '') {
						blocks = [{
							orig: '',
							final: applyStr,
							state: 'done'
						}]
					}
					for (let blockNum = currStreamingBlockNum; blockNum < blocks.length; blockNum += 1) {
						const block = blocks[blockNum];

						if (block.state === 'writingOriginal') {
							// update stream state to the first line of original if some portion of original has been written
							if (shouldUpdateOrigStreamStyle && block.orig.trim().length >= 20) {
								const startingAtLine = diffZone._streamState.line ?? 1; // dont go backwards if already have a stream line
								const originalRange = findTextInCode(block.orig, originalFileCode, startingAtLine);
								if (typeof originalRange !== 'string') {
									const [startLine, _] = convertOriginalRangeToFinalRange(originalRange);
									diffZone._streamState.line = startLine;
									shouldUpdateOrigStreamStyle = false;
								}
							}
							// must be done writing original to move on to writing streamed content
							continue;
						}
						shouldUpdateOrigStreamStyle = true;


						// if this is the first time we're seeing this block, add it as a diffarea so we can start streaming
						if (!(blockNum in addedTrackingZoneOfBlockNum)) {
							const originalBounds = originalFileCode.trim() === '' ? [1, block.final.split('\n').length] as const : findTextInCode(block.orig, originalFileCode);
							// if error
							if (typeof originalBounds === 'string') {
								messages.push(
									{ role: 'assistant', content: fullText }, // latest output
									{ role: 'user', content: errMsgOfInvalidStr(originalBounds) } // user explanation of what's wrong
								);
								if (streamRequestIdRef.current) this._llmMessageService.abort(streamRequestIdRef.current);
								shouldSendAnotherMessage = true;
								revertAndContinueHistory();
								this._notificationService.info(`${uri.fsPath} 一键Apply失败，请手工合入，功能拼命优化中 ···`);
								onDone(false);
								return;
							}

							const [startLine, endLine] = convertOriginalRangeToFinalRange(originalBounds);

							// otherwise if no error, add the position as a diffarea
							const adding: Omit<TrackingZone<SearchReplaceDiffAreaMetadata>, 'diffareaid'> = {
								type: 'TrackingZone',
								applyId,
								sessionId,
								businessEvent: opts.businessEvent,
								startLine: startLine,
								endLine: endLine,
								_URI: uri,
								metadata: {
									originalBounds: [...originalBounds],
									originalCode: block.orig,
								},
							};
							const trackingZone = this._addDiffArea(adding);
							addedTrackingZoneOfBlockNum.push(trackingZone);
							latestStreamLocationMutable = { line: startLine, addedSplitYet: false, col: 1, originalCodeStartLine: 1 };
						} // <-- done adding diffarea


						// should always be in streaming state here
						if (!diffZone._streamState.isStreaming) {
							console.error('DiffZone was not in streaming state in _initializeSearchAndReplaceStream');
							continue;
						}
						if (!latestStreamLocationMutable) continue;

						// if a block is done, finish it by writing all
						if (block.state === 'done') {
							const { startLine: finalStartLine, endLine: finalEndLine } = addedTrackingZoneOfBlockNum[blockNum];
							this._writeText(uri, block.final,
								{ startLineNumber: finalStartLine, startColumn: 1, endLineNumber: finalEndLine, endColumn: Number.MAX_SAFE_INTEGER }, // 1-indexed
								{ shouldRealignDiffAreas: true }
							);
							diffZone._streamState.line = finalEndLine + 1;
							currStreamingBlockNum = blockNum + 1;
							continue;
						}

						// write the added text to the file
						const deltaFinalText = block.final.substring((oldBlocks[blockNum]?.final ?? '').length, Infinity);
						this._writeStreamedDiffZoneLLMText(uri, block.orig, block.final, deltaFinalText, latestStreamLocationMutable);
						oldBlocks = blocks; // oldblocks is only used if writingFinal

						// const { endLine: currentEndLine } = addedTrackingZoneOfBlockNum[blockNum] // would be bad to do this because a lot of the bottom lines might be the same. more accurate to go with latestStreamLocationMutable
						// diffZone._streamState.line = currentEndLine
						diffZone._streamState.line = latestStreamLocationMutable.line;
					} // end for

					this._refreshStylesAndDiffsInURI(uri, true);

					if (blocks.length === 0) {
						this._notificationService.info(`${uri.fsPath} 运行了apply,但是模型没有输出任何更改，请重试或者手工修改。`);
					}

					// writeover the whole file
					let newCode = originalFileCode;
					for (let blockNum = addedTrackingZoneOfBlockNum.length - 1; blockNum >= 0; blockNum -= 1) {
						const { originalBounds } = addedTrackingZoneOfBlockNum[blockNum].metadata;
						const finalCode = blocks[blockNum].final;

						if (finalCode === null) continue;

						const [originalStart, originalEnd] = originalBounds;
						const lines = newCode.split('\n');
						newCode = [
							...lines.slice(0, (originalStart - 1)),
							...finalCode.split('\n'),
							...lines.slice((originalEnd - 1) + 1, Infinity)
						].join('\n');
					}
					const numLines = this._getNumLines(uri);
					if (numLines !== null) {
						this._writeText(uri, newCode,
							{ startLineNumber: 1, startColumn: 1, endLineNumber: numLines, endColumn: Number.MAX_SAFE_INTEGER },
							{ shouldRealignDiffAreas: true }
						);
					}
					const change = originalFileCode !== newCode;
					onDone(change);
				},
				onError: (e) => {
					this._notifyError(e);
					onDone();
					this._undoHistory(uri);
					reporter('', e.message);
				},

			});
		}


		return diffZone;
	}

	private _stopIfStreaming(diffZone: DiffZone) {
		const uri = diffZone._URI;

		const streamRequestId = diffZone._streamState.streamRequestIdRef?.current;
		if (!streamRequestId) return;

		this._llmMessageService.abort(streamRequestId);

		diffZone._streamState = { isStreaming: false, applyBoxId: diffZone._streamState.applyBoxId };
		this._onDidChangeDiffZoneStreaming.fire({ uri, diffareaid: diffZone.diffareaid });
	}

	_undoHistory(uri: URI) {
		this._undoRedoService.undo(uri);
	}

	_interruptSingleDiffZoneStreaming({ diffareaid }: { diffareaid: number }) {
		const diffZone = this.diffAreaOfId[diffareaid];
		if (diffZone?.type !== 'DiffZone') return;
		if (!diffZone._streamState.isStreaming) return;

		this._stopIfStreaming(diffZone);
		this._undoHistory(diffZone._URI);
	}


	isCtrlKZoneStreaming({ diffareaid }: { diffareaid: number }) {
		const ctrlKZone = this.diffAreaOfId[diffareaid];
		if (!ctrlKZone) return false;
		if (ctrlKZone.type !== 'CtrlKZone') return false;
		return !!ctrlKZone._linkedStreamingDiffZone;
	}

	// diffareaid of the ctrlKZone (even though the stream state is dictated by the linked diffZone)
	interruptCtrlKStreaming({ diffareaid }: { diffareaid: number }) {
		const ctrlKZone = this.diffAreaOfId[diffareaid];
		if (ctrlKZone?.type !== 'CtrlKZone') return;
		if (!ctrlKZone._linkedStreamingDiffZone) return;

		const linkedStreamingDiffZone = this.diffAreaOfId[ctrlKZone._linkedStreamingDiffZone];
		if (!linkedStreamingDiffZone) return;
		if (linkedStreamingDiffZone.type !== 'DiffZone') return;

		this._interruptSingleDiffZoneStreaming({ diffareaid: linkedStreamingDiffZone.diffareaid });
	}

	getURIStreamState = ({ uri, applyBoxId }: { uri: URI | null, applyBoxId?: string }) => {
		if (uri === null) return 'idle';
		let diffZones: DiffZone[] = [];
		try {
			diffZones = [...this.diffAreasOfURI[uri.fsPath].values()]
				.map(diffareaid => this.diffAreaOfId[diffareaid])
				.filter(diffArea => !!diffArea && diffArea.type === 'DiffZone');
		} catch (e) {
			debugger;
		}

		// 如果提供了applyBoxId，只检查对应的diffZone
		let relevantDiffZones = diffZones;
		if (applyBoxId) {
			relevantDiffZones = diffZones.filter(diffZone => diffZone._streamState.applyBoxId === applyBoxId);
		}

		const isStreaming = relevantDiffZones.find(diffZone => !!diffZone._streamState.isStreaming);

		// 检查是否有实际的diff内容可以显示
		const hasDiffContent = this._sortedDiffs[uri.fsPath] && this._sortedDiffs[uri.fsPath].length > 0;

		const state: URIStreamState = isStreaming ? 'streaming' : (relevantDiffZones.length === 0 || !hasDiffContent ? 'idle' : 'acceptRejectAll');
		return state;
	};

	interruptURIStreaming({ uri }: { uri: URI }) {
		// brute force for now is OK
		for (const diffareaid of this.diffAreasOfURI[uri.fsPath] || []) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (diffArea?.type !== 'DiffZone') continue;
			if (!diffArea._streamState.isStreaming) continue;
			this._stopIfStreaming(diffArea);
		}
		this._undoHistory(uri);
	}


	// public removeDiffZone(diffZone: DiffZone, behavior: 'reject' | 'accept') {
	// 	const uri = diffZone._URI
	// 	const { onFinishEdit } = this._addToHistory(uri)

	// 	if (behavior === 'reject') this._revertAndDeleteDiffZone(diffZone)
	// 	else if (behavior === 'accept') this._deleteDiffZone(diffZone)

	// 	this._refreshStylesAndDiffsInURI(uri)
	// 	onFinishEdit()
	// }

	private _revertAndDeleteDiffZone(diffZone: DiffZone) {
		const uri = diffZone._URI;

		const writeText = diffZone.originalCode;
		const toRange: IRange = { startLineNumber: diffZone.startLine, startColumn: 1, endLineNumber: diffZone.endLine, endColumn: Number.MAX_SAFE_INTEGER };
		this._writeText(uri, writeText, toRange, { shouldRealignDiffAreas: true });

		this._deleteDiffZone(diffZone);
	}


	// remove a batch of diffareas all at once (and handle accept/reject of their diffs)
	// 注意：_rewriteCodeStream现在直接使用_deleteAllDiffAreas来支持多次apply的累积diff显示
	public removeDiffAreas({ uri, removeCtrlKs, behavior, clearOriginalCache = true }: {
		uri: URI;
		removeCtrlKs: boolean;
		behavior: 'reject' | 'accept';
		clearOriginalCache?: boolean; // 是否清除原始内容缓存，默认true（手动操作）
	}) {

		const diffareaids = this.diffAreasOfURI[uri.fsPath];
		if (diffareaids.size === 0) return; // do nothing
		let applyId = '';
		let businessEvent = '';
		let sessionId = '';
		const diffIds: Set<string> = new Set();
		for (const diffareaid of diffareaids) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (!diffArea) continue;
			if (diffArea.type === 'DiffZone') {
				Object.keys(diffArea._diffOfId).forEach(diffid => diffIds.add(diffid));
			}
			applyId = diffArea.applyId;
			sessionId = diffArea.sessionId;
			businessEvent = diffArea.businessEvent;
		}
		if (behavior === 'accept') {
			this.report(METRICS_EVENT.ACCEPT_ALL, uri.fsPath, applyId, sessionId, diffIds, businessEvent)
		} else {
			this.report(METRICS_EVENT.REJECT_ALL, uri.fsPath, applyId, sessionId, diffIds, businessEvent)

		}

		const { onFinishEdit } = this._addToHistory(uri);

		for (const diffareaid of diffareaids) {
			const diffArea = this.diffAreaOfId[diffareaid];
			if (!diffArea) continue;

			if (diffArea.type == 'DiffZone') {
				if (behavior === 'reject') this._revertAndDeleteDiffZone(diffArea);
				else if (behavior === 'accept') this._deleteDiffZone(diffArea);
			}
			else if (diffArea.type === 'CtrlKZone' && removeCtrlKs) {
				this._deleteCtrlKZone(diffArea);
			}
		}

		this._refreshStylesAndDiffsInURI(uri, true);

		// 触发diff计数变化事件
		this._updateDiffCount(uri);

		// 检查是否还有diff区域，如果没有则清除原始内容缓存（手动Accept All / Reject All后）
		if (clearOriginalCache && this.diffAreasOfURI[uri.fsPath]?.size === 0) {
			this.originalFileContentBeforeAnyApply.delete(uri.fsPath);
		}

		onFinishEdit();
	}

	// called on codeseek.acceptDiff
	public async acceptDiff({ diffid }: { diffid: number }) {
		const diff = this.diffOfId[diffid];
		if (!diff) return;
		const sessionId = diff.sessionId;

		const { diffareaid } = diff;
		const diffArea = this.diffAreaOfId[diffareaid];
		if (!diffArea) return;

		if (diffArea.type !== 'DiffZone') return;

		const uri = diffArea._URI;

		// add to history
		const { onFinishEdit } = this._addToHistory(uri);

		const originalLines = diffArea.originalCode.split('\n');
		let newOriginalCode: string;

		if (diff.type === 'deletion') {
			newOriginalCode = [
				...originalLines.slice(0, (diff.originalStartLine - 1)), // everything before startLine
				// <-- deletion has nothing here
				...originalLines.slice((diff.originalEndLine - 1) + 1, Infinity) // everything after endLine
			].join('\n');
		}
		else if (diff.type === 'insertion') {
			newOriginalCode = [
				...originalLines.slice(0, (diff.originalStartLine - 1)), // everything before startLine
				diff.code, // code
				...originalLines.slice((diff.originalStartLine - 1), Infinity) // startLine (inclusive) and on (no +1)
			].join('\n');
		}
		else if (diff.type === 'edit') {
			newOriginalCode = [
				...originalLines.slice(0, (diff.originalStartLine - 1)), // everything before startLine
				diff.code, // code
				...originalLines.slice((diff.originalEndLine - 1) + 1, Infinity) // everything after endLine
			].join('\n');
		}
		else {
			throw new Error(`Codeseek error: ${diff}.type not recognized`);
		}

		this.report(METRICS_EVENT.ACCEPT, uri.fsPath, diffArea.applyId, sessionId, new Set([diffid.toString()]), diffArea.businessEvent);

		// update code now accepted as original
		diffArea.originalCode = newOriginalCode;

		// 方案A：只将accept的diff精确应用到原始基准上
		// 避免隐式accept其他pending的diff
		this._updateBaselineWithAcceptedDiff(uri, diff);

		// delete the diff
		this._deleteDiff(diff);

		// diffArea should be removed if it has no more diffs in it
		if (Object.keys(diffArea._diffOfId).length === 0) {
			this._deleteDiffZone(diffArea);
		}

		this._refreshStylesAndDiffsInURI(uri, true);

		// 触发diff计数变化事件
		this._updateDiffCount(uri);

		// 检查是否还有diff区域，如果没有则清除原始内容缓存
		if (this.diffAreasOfURI[uri.fsPath]?.size === 0) {
			this.originalFileContentBeforeAnyApply.delete(uri.fsPath);
		}

		onFinishEdit();
	}

	/**
	 * 将accept的diff精确应用到原始基准上，避免隐式accept其他pending的diff
	 */
	private _updateBaselineWithAcceptedDiff(uri: URI, acceptedDiff: Diff): void {
		const currentBaseline = this.originalFileContentBeforeAnyApply.get(uri.fsPath);
		if (!currentBaseline) return;

		const baselineLines = currentBaseline.split('\n');
		const diffArea = this.diffAreaOfId[acceptedDiff.diffareaid];
		if (!diffArea || diffArea.type !== 'DiffZone') return;

		try {
			switch (acceptedDiff.type) {
				case 'insertion':
					// insertion类型没有originalEndLine，只在指定位置插入
					const absoluteInsertLine = (diffArea.startLine - 1) + (acceptedDiff.originalStartLine - 1);
					const insertLines = acceptedDiff.code.split('\n');
					baselineLines.splice(absoluteInsertLine, 0, ...insertLines);
					break;

				case 'deletion':
					// deletion类型有originalEndLine，删除指定范围
					const absoluteDeleteStart = (diffArea.startLine - 1) + (acceptedDiff.originalStartLine - 1);
					const absoluteDeleteEnd = (diffArea.startLine - 1) + (acceptedDiff.originalEndLine - 1);
					const deleteCount = absoluteDeleteEnd - absoluteDeleteStart + 1;
					baselineLines.splice(absoluteDeleteStart, deleteCount);
					break;

				case 'edit':
					// edit类型有originalEndLine，替换指定范围
					const absoluteEditStart = (diffArea.startLine - 1) + (acceptedDiff.originalStartLine - 1);
					const absoluteEditEnd = (diffArea.startLine - 1) + (acceptedDiff.originalEndLine - 1);
					const editLines = acceptedDiff.code.split('\n');
					const replaceCount = absoluteEditEnd - absoluteEditStart + 1;
					baselineLines.splice(absoluteEditStart, replaceCount, ...editLines);
					break;

				default:
					// TypeScript会检查这里是never类型，说明所有情况都已覆盖
					this._codeseekLogService.warn(`Unknown diff type: ${(acceptedDiff as any).type}`);
					return;
			}

			// 更新基准
			const updatedBaseline = baselineLines.join('\n');
			this.originalFileContentBeforeAnyApply.set(uri.fsPath, updatedBaseline);

		} catch (error) {
			this._codeseekLogService.error('Error updating baseline with accepted diff:', error);
		}
	}

	// called on codeseek.rejectDiff
	public rejectDiff({ diffid }: { diffid: number }) {

		const diff = this.diffOfId[diffid];
		if (!diff) return;

		const { diffareaid } = diff;
		const diffArea = this.diffAreaOfId[diffareaid];
		if (!diffArea) return;

		if (diffArea.type !== 'DiffZone') return;

		const uri = diffArea._URI;

		// add to history
		const { onFinishEdit } = this._addToHistory(uri);

		let writeText: string;
		let toRange: IRange;

		// if it was a deletion, need to re-insert
		// (this image applies to writeText and toRange, not newOriginalCode)
		//  A
		// |B   <-- deleted here, diff.startLine == diff.endLine
		//  C
		if (diff.type === 'deletion') {
			// if startLine is out of bounds (deleted lines past the diffarea), applyEdit will do a weird rounding thing, to account for that we apply the edit the line before
			if (diff.startLine - 1 === diffArea.endLine) {
				writeText = '\n' + diff.originalCode;
				toRange = { startLineNumber: diff.startLine - 1, startColumn: Number.MAX_SAFE_INTEGER, endLineNumber: diff.startLine - 1, endColumn: Number.MAX_SAFE_INTEGER };
			}
			else {
				writeText = diff.originalCode + '\n';
				toRange = { startLineNumber: diff.startLine, startColumn: 1, endLineNumber: diff.startLine, endColumn: 1 };
			}
		}
		else if (diff.type === 'insertion') {
			// handle the case where the insertion was a newline at end of diffarea (applying to the next line doesnt work because it doesnt exist, vscode just doesnt delete the correct # of newlines)
			if (diff.endLine === diffArea.endLine) {
				// delete the line before instead of after
				writeText = '';
				toRange = { startLineNumber: diff.startLine - 1, startColumn: Number.MAX_SAFE_INTEGER, endLineNumber: diff.endLine, endColumn: 1 }; // 1-indexed
			}
			else {
				writeText = '';
				toRange = { startLineNumber: diff.startLine, startColumn: 1, endLineNumber: diff.endLine + 1, endColumn: 1 }; // 1-indexed
			}

		}
		else if (diff.type === 'edit') {
			writeText = diff.originalCode;
			toRange = { startLineNumber: diff.startLine, startColumn: 1, endLineNumber: diff.endLine, endColumn: Number.MAX_SAFE_INTEGER }; // 1-indexed
		}
		else {
			throw new Error(`Codeseek error: ${diff}.type not recognized`);
		}

		this.report(METRICS_EVENT.REJECT, uri.fsPath, diffArea.applyId, diff.sessionId, new Set([diffid.toString()]), diffArea.businessEvent);
		// update the file
		this._writeText(uri, writeText, toRange, { shouldRealignDiffAreas: true });

		// originalCode does not change!

		// delete the diff
		this._deleteDiff(diff);

		// diffArea should be removed if it has no more diffs in it
		if (Object.keys(diffArea._diffOfId).length === 0) {
			this._deleteDiffZone(diffArea);
		}

		this._refreshStylesAndDiffsInURI(uri, true);

		// 触发diff计数变化事件
		this._updateDiffCount(uri);

		// 检查是否还有diff区域，如果没有则清除原始内容缓存
		if (this.diffAreasOfURI[uri.fsPath]?.size === 0) {
			this.originalFileContentBeforeAnyApply.delete(uri.fsPath);
		}

		onFinishEdit();

	}

	/**
	 * 更新当前文件的diff计数并触发事件
	 * @param uri 文件URI
	 */
	private _updateDiffCount(uri: URI): void {
		const allDiffs = this._sortedDiffs[uri.fsPath];
		if (allDiffs.length > 0) {
			const editor = this._editorService.activeEditorPane?.getControl() as ICodeEditor | undefined;
			if (editor) {
				const position = editor.getPosition();
				if (position) {
					// 找到当前光标位置所在或之后最近的diff
					for (let i = 0; i < allDiffs.length; i++) {
						if (allDiffs[i].startLine >= position.lineNumber) {
							this._currentDiffIndex = i;
							break;
						}
						// 如果是最后一个diff且光标在其后，则选中最后一个
						if (i === allDiffs.length - 1) {
							this._currentDiffIndex = i;
						}
					}
				}
			}
		}

		// 触发事件
		this._onDidChangeDiffCount.fire({ uri, currentIndex: this._currentDiffIndex, totalDiffs: allDiffs.length });
	}

	public getCurrentDiffIndex(): number {
		return this._currentDiffIndex;
	}

	public acceptDiffAtIndex({ uri, index }: { uri: URI; index: number }): void {
		const diffs = this._sortedDiffs[uri.fsPath];
		if (!diffs || index < 0 || index >= diffs.length) {
			return;
		}

		this.acceptDiff({ diffid: diffs[index].diffid });
	}

	public rejectDiffAtIndex({ uri, index }: { uri: URI; index: number }): void {
		const diffs = this._sortedDiffs[uri.fsPath];
		if (!diffs || index < 0 || index >= diffs.length) {
			return;
		}

		this.rejectDiff({ diffid: diffs[index].diffid });
	}

	private report(eventType: METRICS_EVENT_TYPE, filePath: string, applyId: string, sessionId: string, diffids: Set<string>, businessEvent: string) {
		const codeSnippet = [];
		for (const diffid of diffids) {
			const diff: ComputedDiff = this.diffOfId[diffid];
			if (!diff) continue;
			if (diff.type === "deletion") continue;
			if (diff.type !== 'insertion' && diff.originalCode === diff.code) continue;
			if (diff.type === 'insertion' && diff.code === '') continue;
			codeSnippet.push({
				id: diffid,
				original: diff.type === 'insertion' ? '' : diff.originalCode,
				update: diff.code,
				codeLine: diff.code.split('\n').length,
			})
		}
		if (codeSnippet.length == 0) return;

		const data = [{
			repo: this._contextKeyService.getContextKeyValue<string>('scmActiveWholeRepositoryName') || '',
			filePath: filePath,
			filePathInRepo: this.getFilePathInRepo(filePath),
			codeSnippet: codeSnippet,
			totalLine: codeSnippet.map((x) => x.codeLine).reduce((a, b) => a + b, 0),
		}]
		const traceId = 'accept-' + generateUuid();

		const metricsData = {
			eventContent: { data: data, totalLine: data.map((x) => x.totalLine).reduce((a, b) => a + b, 0), businessEvent },
			traceIdPair: { traceId, parentTraceId: applyId, sessionId },
		}
		this._metricsService.capture(eventType, metricsData);
	}

	private getFilePathInRepo(filePath: string): string {
		const repo = this._contextKeyService.getContextKeyValue<string>('scmActiveRepositoryRootPath') || '';
		if (repo) {
			return filePath.substring(repo.length + 1);
		}
		return filePath;
	}
}

registerSingleton(IEditCodeService, EditCodeService, InstantiationType.Eager);
